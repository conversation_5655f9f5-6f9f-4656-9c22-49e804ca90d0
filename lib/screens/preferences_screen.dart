import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/common_things/my_constants.dart';
import 'package:zupid/models/myUserInfo.dart';
import 'package:zupid/models/myUserPrefs.dart';
import 'package:zupid/providers/auth_provider.dart';
import 'package:zupid/widgets/loading_widget.dart';
import '../common_things/analytics.dart';
import '../providers/database_provider.dart';

class PreferencesScreen extends StatefulWidget {
  static const routeName = '/preferences-screen';

  @override
  State<PreferencesScreen> createState() => _PreferencesScreenState();
}

class _PreferencesScreenState extends State<PreferencesScreen> {
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  bool _isLoading = false;

  double maximumDistanceAllowed = 0;
  RangeValues _currentRangeValues = RangeValues(18, 60);
  var dating = 0;
  var lookingFor = 0;
  double minAge = 20;
  double maxAge = 30;
  double maxDistance = 50;
  bool firsTimeRun = false;
  bool verified = false;

  void firstTimeRunFunc(MyUserPrefs _myUserPrefs, MyUserInfo _myUserInfo) {
    lookingFor = _myUserInfo.lookingFor;
    dating = _myUserPrefs.dating;
    minAge = _myUserPrefs.minAge.toDouble();
    maxAge = _myUserPrefs.maxAge.toDouble();
    _currentRangeValues = RangeValues(minAge, maxAge);
    maxDistance = _myUserPrefs.maxDistance.toDouble();
    verified = _myUserPrefs.verified;
  }

  @override
  Widget build(BuildContext context) {
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('PreferencesScreen', {});
      _isAnalytics = false;
    }

    final _myUserPrefs =
        Provider.of<DatabaseProvider>(context, listen: true).getMyUserPrefs;
    final _myUserInfo =
        Provider.of<DatabaseProvider>(context, listen: true).getMyUserInfo;

    if (firsTimeRun == false) {
      firstTimeRunFunc(_myUserPrefs, _myUserInfo);
      firsTimeRun = true;
      final distanceParams = Provider.of<AuthProvider>(context, listen: false)
          .getJsonDistanceParamters;
      if (_myUserInfo.isMembership) {
        maximumDistanceAllowed = distanceParams[1];
      } else {
        maximumDistanceAllowed = distanceParams[0];
      }
      if (maxDistance > maximumDistanceAllowed) {
        maxDistance = maximumDistanceAllowed;
      }
      firsTimeRun = true;
    }

    return WillPopScope(
      onWillPop: () async {
        try {
          Map<String, dynamic> jsonBody = {
            "looking_for": lookingFor,
            "dating": dating,
            "min_age": int.parse(_currentRangeValues.start.round().toString()),
            "max_age": int.parse(_currentRangeValues.end.round().toString()),
            "max_distance": int.parse(maxDistance.round().toString()),
            "verified": verified,
          };
          Provider.of<DatabaseProvider>(context, listen: false)
              .updateUserPrefs(updateUserPrefsRoute, jsonBody);
        } catch (error) {}
        return true;
      },
      child: Scaffold(
        backgroundColor: mainColor,
        appBar: AppBar(
          elevation: 0,
          backgroundColor: mainColor,
          title: Text(
            LocaleKeys.PreferencesScreen_Select_preferences.tr(),
            style: TextStyle(
              color: mainContrastColor,
              fontWeight: FontWeight.bold,
              fontSize: 14.sp, // Reduced from 16.sp
            ),
          ),
          iconTheme: IconThemeData(color: mainContrastColor),
          toolbarHeight: 7.h, // Reduced app bar height
        ),
        body: _isLoading
            ? LoadingWidget()
            : SingleChildScrollView(
                padding: EdgeInsets.symmetric(
                    horizontal: 5.w, vertical: 1.h), // Reduced vertical padding
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Looking For Section
                    Text(
                      LocaleKeys.PreferencesScreen_Looking_for.tr(),
                      style: TextStyle(
                        fontSize: 12.sp, // Reduced from 14.sp
                        fontWeight: FontWeight.bold,
                        color: mainContrastColor,
                      ),
                    ),
                    SizedBox(height: 1.h), // Reduced spacing
                    Wrap(
                      spacing: 1.5.w, // Reduced spacing
                      runSpacing: 1.5.w, // Reduced spacing
                      children: [
                        _buildLookingForChip(
                          0,
                          LocaleKeys.PreferencesScreen_Long_Term.tr(),
                        ),
                        _buildLookingForChip(
                          1,
                          LocaleKeys.PreferencesScreen_Short_Term.tr(),
                        ),
                        _buildLookingForChip(
                          2,
                          LocaleKeys.PreferencesScreen_Dont_know_yet.tr(),
                        ),
                      ],
                    ),
                    SizedBox(height: 3.h), // Reduced spacing

                    // Age Range Section
                    Text(
                      LocaleKeys.PreferencesScreen_Choose_Age.tr(),
                      style: TextStyle(
                        fontSize: 12.sp, // Reduced from 14.sp
                        fontWeight: FontWeight.bold,
                        color: mainContrastColor,
                      ),
                    ),
                    SizedBox(height: 1.h), // Reduced spacing
                    Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: 3.w, vertical: 2.h), // Reduced padding
                      decoration: BoxDecoration(
                        color: Colors.grey[900],
                        borderRadius: BorderRadius.circular(
                            12), // Slightly smaller radius
                      ),
                      child: Column(
                        children: [
                          RangeSlider(
                            activeColor: mainContrastColor,
                            inactiveColor: Colors.grey[700]!,
                            values: _currentRangeValues,
                            min: 18,
                            max: 60,
                            divisions: 42,
                            labels: RangeLabels(
                              _currentRangeValues.start.round().toString(),
                              _currentRangeValues.end.round().toString(),
                            ),
                            onChanged: (RangeValues values) {
                              setState(() {
                                _currentRangeValues = values;
                              });
                            },
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 3.w),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  '18',
                                  style: TextStyle(
                                    fontSize: 10.sp, // Reduced from 12.sp
                                    color: mainContrastColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  '60',
                                  style: TextStyle(
                                    fontSize: 10.sp, // Reduced from 12.sp
                                    color: mainContrastColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 3.h), // Reduced spacing

                    // Distance Section
                    Text(
                      LocaleKeys.PreferencesScreen_Choose_Maximum_Distance.tr(),
                      style: TextStyle(
                        fontSize: 12.sp, // Reduced from 14.sp
                        fontWeight: FontWeight.bold,
                        color: mainContrastColor,
                      ),
                    ),
                    SizedBox(height: 1.h), // Reduced spacing
                    Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: 3.w, vertical: 2.h), // Reduced padding
                      decoration: BoxDecoration(
                        color: Colors.grey[900],
                        borderRadius: BorderRadius.circular(
                            12), // Slightly smaller radius
                      ),
                      child: Column(
                        children: [
                          Slider(
                            activeColor: mainContrastColor,
                            inactiveColor: Colors.grey[700]!,
                            value: maxDistance,
                            min: 2,
                            max: maximumDistanceAllowed,
                            divisions: (maximumDistanceAllowed - 2) ~/ 1,
                            label: maxDistance.round().toString(),
                            onChanged: (newValue) {
                              setState(() {
                                maxDistance = newValue;
                              });
                            },
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 3.w),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  '2 ${LocaleKeys.PreferencesScreen_km.tr()}',
                                  style: TextStyle(
                                    fontSize: 10.sp, // Reduced from 12.sp
                                    color: mainContrastColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  '${maximumDistanceAllowed.toStringAsFixed(0)} ${LocaleKeys.PreferencesScreen_km.tr()}',
                                  style: TextStyle(
                                    fontSize: 10.sp, // Reduced from 12.sp
                                    color: mainContrastColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 3.h), // Reduced spacing

                    // Verified Only Section
                    Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: 5.w, vertical: 2.h), // Reduced padding
                      decoration: BoxDecoration(
                        color: Colors.grey[900],
                        borderRadius: BorderRadius.circular(
                            12), // Slightly smaller radius
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Show Verified Profiles Only',
                            style: TextStyle(
                              fontSize: 12.sp, // Reduced from 14.sp
                              fontWeight: FontWeight.bold,
                              color: mainContrastColor,
                            ),
                          ),
                          Transform.scale(
                            scale: 1.2, // Reduced from 1.4
                            child: Switch(
                              activeColor: mainContrastColor,
                              activeTrackColor:
                                  mainContrastColor.withOpacity(0.3),
                              inactiveThumbColor: Colors.grey[600],
                              inactiveTrackColor: Colors.grey[800],
                              value: verified,
                              onChanged: (newValue) {
                                setState(() {
                                  verified = newValue;
                                });
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildLookingForChip(int index, String label) {
    final isSelected = lookingFor == index;
    return ChoiceChip(
      label: Text(
        label,
        style: TextStyle(
          color: isSelected ? mainColor : Colors.white,
          fontSize: 9.sp, // Reduced from 11.sp
          fontWeight: FontWeight.w500,
        ),
      ),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          lookingFor = selected ? index : lookingFor;
        });
      },
      backgroundColor: Colors.grey[800],
      selectedColor: mainContrastColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(18), // Slightly smaller radius
      ),
      padding: EdgeInsets.symmetric(
          horizontal: 4.w, vertical: 1.h), // Reduced padding
      labelPadding: EdgeInsets.symmetric(horizontal: 1.w), // Reduced padding
    );
  }
}
