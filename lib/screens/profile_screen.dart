import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:zupid/common_things/images.dart';
import 'package:zupid/screens/buy_premium_cashfree.dart';
import 'package:zupid/screens/movie_search_screen.dart';
import 'package:zupid/screens/referral_screen.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/models/myUserInfo.dart';
import 'package:zupid/providers/database_provider.dart';
import 'package:zupid/screens/buy_premium_screen.dart';
import 'package:zupid/screens/edit_info_screen.dart';
import 'package:zupid/screens/match_screen.dart';
import 'package:zupid/screens/preferences_screen.dart';
import 'package:zupid/screens/premium_screen.dart';
import 'package:zupid/screens/settings_screen.dart';
import 'package:zupid/widgets/loading_widget.dart';
import 'package:url_launcher/url_launcher.dart';

import '../common_things/analytics.dart';
import '../common_things/my_fucntions.dart';
import '../widgets/photos_container.dart';

class ProfileScreen extends StatefulWidget {
  static const routeName = 'profile-screen';

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  /////
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  //////
  List<String> _photos = [];

  bool _isLoading = false;
  bool firstRun = true;
  // MyUserInfo? _myUserInfo;
  //
  void _showCompleteInfoDialog(MyUserInfo _myUserInfo) {
    bool photos = false;
    bool lifeStory = false;
    bool interest = false;
    bool prompt1 = false;
    bool prompt2 = false;
    //
    if (_myUserInfo.photosName.contains("-1") == false) {
      photos = true;
    }
    if (_myUserInfo.aboutMe != "") {
      lifeStory = true;
    }

    if (_myUserInfo.allInterests.interests.length != 0) {
      interest = true;
    }

    if (_myUserInfo.prompt1Id != "") {
      prompt1 = true;
    }
    if (_myUserInfo.prompt2Id != "") {
      prompt2 = true;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) => Dialog(
        child: Padding(
          padding: EdgeInsets.all(3.h),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              eachUserInfoItem('Photos', photos),
              eachUserInfoItem('Life Story', lifeStory),
              eachUserInfoItem('Interest', interest),
              eachUserInfoItem('Prompt 1', prompt1),
              eachUserInfoItem('Prompt 2', prompt2),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    //////////
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('ProfileScreen', {});
      _isAnalytics = false;
    }

    ////////////
    // first load myuser info from dbprov

    //
    return _isLoading
        ? LoadingWidget()
        : SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.all(1.w),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // if (_myUserInfo!.isProfileComplete == false)
                  //   GestureDetector(
                  //     onTap: () {
                  //       _showCompleteInfoDialog(_myUserInfo!);
                  //     },
                  //     child: Align(
                  //       alignment: Alignment.center,
                  //       child: Row(
                  //         mainAxisAlignment: MainAxisAlignment.center,
                  //         crossAxisAlignment: CrossAxisAlignment.center,
                  //         children: [
                  //           Row(
                  //             mainAxisAlignment: MainAxisAlignment.center,
                  //             crossAxisAlignment: CrossAxisAlignment.center,
                  //             children: [
                  //               Column(
                  //                 mainAxisAlignment: MainAxisAlignment.center,
                  //                 crossAxisAlignment: CrossAxisAlignment.center,
                  //                 children: [
                  //                   Text(
                  //                     "Your profile is not shown to others",
                  //                     style: TextStyle(
                  //                       fontSize: 8.sp,
                  //                       fontWeight: FontWeight.w500,
                  //                       color: Colors.grey,
                  //                     ),
                  //                   ),
                  //                   SizedBox(height: .5.h),
                  //                   Row(
                  //                     children: [
                  //                       Text(
                  //                         "Complete your Profile to get visible",
                  //                         style: TextStyle(
                  //                           fontSize: 8.sp,
                  //                           fontWeight: FontWeight.w500,
                  //                           color: Colors.grey,
                  //                         ),
                  //                       ),
                  //                       SizedBox(width: 1.w),
                  //                       Icon(
                  //                         size: 10.sp,
                  //                         Icons.info,
                  //                         color: Colors.grey,
                  //                       ),
                  //                     ],
                  //                   ),
                  //                 ],
                  //               ),
                  //             ],
                  //           ),
                  //         ],
                  //       ),
                  //     ),
                  //   ),

                  SizedBox(height: 3.h),
                  ProfilePic(),
                  SizedBox(height: 2.h),
                  ListTile(
                    dense: true,
                    minLeadingWidth: 0,
                    onTap: () {
                      Navigator.of(context)
                          .pushNamed(PreferencesScreen.routeName);
                    },
                    leading: const Icon(
                      Icons.list_rounded,
                      color: mainContrastColor,
                    ),
                    title: Text(
                      LocaleKeys.ProfileScreen_preferences.tr(),
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        color: mainContrastColor,
                      ),
                    ),
                  ),
                  // ListTile(
                  //   dense: true,
                  //   minLeadingWidth: 0,
                  //   onTap: () {
                  //     Navigator.of(context).pushNamed(EditInfoScreen.routeName);
                  //   },
                  //   leading: const Icon(
                  //     Icons.edit,
                  //     color: Colors.black,
                  //   ),
                  //   title: Text(
                  //     LocaleKeys.ProfileScreen_Edit_information.tr(),
                  //     style: const TextStyle(
                  //       color: Colors.black87,
                  //       fontWeight: FontWeight.w500,
                  //     ),
                  //   ),
                  // ),
                  //
                  ListTile(
                    dense: true,
                    minLeadingWidth: 0,
                    onTap: () {
                      Navigator.of(context).pushNamed(SettingsScreen.routeName);
                    },
                    leading: const Icon(
                      Icons.settings_applications_rounded,
                      color: mainContrastColor,
                    ),
                    title: Text(
                      LocaleKeys.ProfileScreen_Settings.tr(),
                      style: const TextStyle(
                        color: mainContrastColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  ListTile(
                    dense: true,
                    minLeadingWidth: 0,
                    onTap: () {
                      Navigator.of(context).pushNamed(ReferralScreen.routeName);
                    },
                    leading: const Icon(
                      Icons.attach_money_outlined,
                      color: mainContrastColor,
                    ),
                    title: const Text(
                      'Refer & Earn Premium',
                      style: TextStyle(
                        color: mainContrastColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  SizedBox(height: 1.h),
                  ProfileScreenPremiumWidget(),
                ],
              ),
            ),
          );
  }
}

class ProfileScreenPremiumWidget extends StatefulWidget {
  const ProfileScreenPremiumWidget({super.key});

  @override
  State<ProfileScreenPremiumWidget> createState() =>
      _ProfileScreenPremiumWidgetState();
}

class _ProfileScreenPremiumWidgetState
    extends State<ProfileScreenPremiumWidget> {
  MyUserInfo? _myUserInfo;
  @override
  Widget build(BuildContext context) {
    _myUserInfo =
        Provider.of<DatabaseProvider>(context, listen: true).getMyUserInfo;
    return Padding(
      padding: EdgeInsets.all(1.w),
      child: Card(
        color: Colors.white70,
        elevation: 1.sp,
        shadowColor: mainColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(2.w),
        ),
        child: Container(
          decoration: BoxDecoration(
            gradient: const LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [mainContrastColor, thirdColor]),
            // borderRadius:
            //     BorderRadius.circular(5.w), // Adjust for capsule shape
          ),
          child: Column(
            children: [
              SizedBox(height: 3.h),
              if (_myUserInfo!.isMembership == false)
                Center(
                  child: ElevatedButton(
                    style: ButtonStyle(
                        padding: MaterialStateProperty.all(
                          EdgeInsets.symmetric(
                              vertical: 1.5.h, horizontal: 10.w),
                        ),
                        shape:
                            MaterialStateProperty.all<RoundedRectangleBorder>(
                          RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(3.h),
                          ),
                        ),
                        backgroundColor: MaterialStatePropertyAll(mainColor)),
                    onPressed: () {
                      // Navigator.of(context)
                      //     .pushNamed(MovieSearchScreen.routeName);
                      // Navigator.of(context)
                      //     .pushNamed(BuyPremiumCashfreeScreen.routeName);
                      Navigator.of(context).pushNamed(ReferralScreen.routeName);
                    },
                    child: Text(
                      LocaleKeys.PremiumFeatures_Buy_Premium.tr(),
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12.5.sp,
                      ),
                    ),
                  ),
                ),
              if (_myUserInfo!.isMembership)
                Center(
                  child: Column(
                    children: [
                      ElevatedButton(
                        style: ButtonStyle(
                            padding: MaterialStateProperty.all(
                              EdgeInsets.symmetric(
                                  vertical: 1.5.h, horizontal: 10.w),
                            ),
                            shape: MaterialStateProperty.all<
                                RoundedRectangleBorder>(
                              RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(3.h),
                              ),
                            ),
                            backgroundColor:
                                MaterialStatePropertyAll(mainColor)),
                        onPressed: () {
                          Navigator.of(context)
                              .pushNamed(PremiumScreen.routeName);
                        },
                        child: Text(
                          LocaleKeys.PremiumFeatures_zupid_Premium.tr(),
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12.5.sp,
                          ),
                        ),
                      ),
                      // SizedBox(height: 1.h),
                      // Text(
                      //   'Valid until 29-02-2025',
                      //   style: TextStyle(
                      //       color: Colors.grey,
                      //       fontSize: 11.sp,
                      //       fontWeight: FontWeight.w600),
                      // ),
                      // SizedBox(height: 1.h),
                    ],
                  ),
                ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 1.h),
                  if (_myUserInfo!.isMembership == false)
                    Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: 5.w, vertical: 1.h),
                      child: Text(
                        LocaleKeys.PremiumFeatures_Upgrade_to_premium.tr(),
                        style: TextStyle(
                          color: mainColor,
                          fontSize: 17.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  if (_myUserInfo!.isMembership)
                    Align(
                      alignment: Alignment.center,
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: 5.w, vertical: 1.h),
                        child: Text(
                          "Enjoy your free premium!",
                          style: TextStyle(
                            color: Colors.black87,
                            fontSize: 14.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 5.w, vertical: 1.h),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildFeatureItem(
                          LocaleKeys.PremiumFeatures_1_title.tr(),
                          LocaleKeys.PremiumFeatures_1_desc.tr(),
                        ),
                        _buildFeatureItem(
                          LocaleKeys.PremiumFeatures_2_title.tr(),
                          LocaleKeys.PremiumFeatures_2_desc.tr(),
                        ),
                        // _buildFeatureItem(
                        //   LocaleKeys.PremiumFeatures_3_title.tr(),
                        //   LocaleKeys.PremiumFeatures_3_desc.tr(),
                        // ),
                        _buildFeatureItem(
                          LocaleKeys.PremiumFeatures_4_title.tr(),
                          LocaleKeys.PremiumFeatures_4_desc.tr(),
                        ),
                        _buildFeatureItem(
                          LocaleKeys.PremiumFeatures_5_title.tr(),
                          LocaleKeys.PremiumFeatures_5_desc.tr(),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(height: 1.h),
            ],
          ),
        ),
      ),
    );
  }
}

Widget _buildFeatureItem(String title, String description) {
  return Container(
    width: 100.w,
    child: Padding(
      padding: EdgeInsets.symmetric(vertical: 1.5.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.check_circle_outline,
            color: mainColor,
            size: 2.5.h,
          ),
          SizedBox(width: 2.h),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 12.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 0.5.h),
                Text(
                  description,
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    color: Colors.black54,
                    fontSize: 10.sp,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}

Widget eachUserInfoItem(String name, bool isThere) {
  return Column(
    children: [
      Row(
        children: [
          Icon(
            isThere ? Icons.check_circle : Icons.cancel,
            color: isThere ? Colors.green : Colors.red,
            size: 12.sp,
          ),
          SizedBox(width: 4.w),
          Text(
            name,
            style: TextStyle(
              fontSize: 12.sp,
            ),
          ),
        ],
      ),
      SizedBox(height: .5.h),
    ],
  );
}

class ProfilePic extends StatefulWidget {
  const ProfilePic({super.key});

  @override
  State<ProfilePic> createState() => _ProfilePicState();
}

class _ProfilePicState extends State<ProfilePic> {
  List<String> _photos = [];
  MyUserInfo? _myUserInfo;

  @override
  Widget build(BuildContext context) {
    _myUserInfo =
        Provider.of<DatabaseProvider>(context, listen: true).getMyUserInfo;
    // firstRun = false;
    _photos = Provider.of<DatabaseProvider>(context, listen: true)
        .getUserSignedUrlMap[_myUserInfo!.userId]!;
    final tempImageDirectory =
        Provider.of<DatabaseProvider>(context, listen: false)
            .tempDirectoryForImage;
    // print('Profile Pic');
    return GestureDetector(
      onTap: (() => Navigator.of(context).pushNamed(EditInfoScreen.routeName)),
      child: Column(
        children: [
          Container(
            width: 30.w, // Adjust width to match the diameter of CircleAvatar
            height: 30.w, // Adjust height to match the diameter of CircleAvatar
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: mainContrastColor,
                width: 3.sp, // Border width
              ),
            ),
            child: Padding(
              padding: EdgeInsets.all(.5.w),
              child: CircleAvatar(
                radius: 15.w, // Adjust radius for the circle size
                backgroundColor: _photos[0] == "-1"
                    ? Colors.grey.shade200
                    : Colors.transparent,
                backgroundImage: _photos[0] != "-1"
                    ? FileImage(
                        File(tempImageDirectory + _photos[0]),
                      ) as ImageProvider // Cast FileImage to ImageProvider
                    : AssetImage(
                        MyImages.kNoPhoto,
                      ) as ImageProvider, // Cast AssetImage to ImageProvider
              ),
            ),
          ),
          SizedBox(
            height: 1.25.h,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                width: 4.w,
              ),
              Text(
                overflow: TextOverflow.ellipsis,
                '${capitalizeFirstLetter(_myUserInfo!.firstName)}, ${findAge(_myUserInfo!.dob)}',
                style: TextStyle(
                  color: thirdColor,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(width: 1.w),
              if (_myUserInfo!.isVerified)
                Icon(
                  Icons.verified,
                  color: Colors.blue,
                  size: 12.sp,
                ),
            ],
          ),

          // const PhotosContainer(
          //   showOnly2: false,
          // ),
          SizedBox(height: 1.h),
          Chip(
            // elevation: 0,
            label: Text(
              'Edit Profile',
              style: TextStyle(
                color: mainContrastColor,
                fontSize: 10.sp, // Adjust font size as needed
                fontWeight: FontWeight.w600, // Modern bold look
                letterSpacing: 0.5, // Slight letter spacing for a cleaner feel
              ),
            ),
            backgroundColor: Colors.black, // Primary color
            padding: EdgeInsets.symmetric(
              horizontal: 1.5.w, // Adjust padding
              vertical: .75.w, // Sleek vertical padding
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(30), // Softer, rounded edges
              side: BorderSide.none, // Remove any possible border
            ),
          ),
        ],
      ),
    );
  }
}
