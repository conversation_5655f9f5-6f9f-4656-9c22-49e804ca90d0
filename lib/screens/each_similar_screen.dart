import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:zupid/widgets/display_user_info.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/models/myChatDetails.dart';
import 'package:zupid/models/myUserInfo.dart';
import 'package:zupid/screens/match_screen.dart';
import 'package:zupid/widgets/interests_tab.dart';
import 'package:zupid/widgets/loading_widget.dart';
import 'package:zupid/widgets/personality_tab.dart';

import '../common_things/analytics.dart';
import '../common_things/my_fucntions.dart';
import '../providers/database_provider.dart';
import '../widgets/my_stack_button.dart';

class EachSimilarScreen extends StatefulWidget {
  static const routeName = '/each-similar-screen';
  @override
  State<EachSimilarScreen> createState() => _EachSimilarScreenState();
}

class _EachSimilarScreenState extends State<EachSimilarScreen>
    with SingleTickerProviderStateMixin {
  /////
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
        length: 3, vsync: this); // 3 tabs: Looks, Personality, Interest
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  //////
  // final double _appBarHeight = kToolbarHeight;
  // final double _bottomNavHeight = kBottomNavigationBarHeight;
  // final double _topBarHeight =
  List<String> _photos = [];
  bool _isLoading = false;

  //

  Future<void> onTapFunc(
      int tapType, MyUserInfo varUserInfo, index, type, message) async {
    setState(() {
      _isLoading = true;
    });

    MyChatDetails? varChatDetails =
        await Provider.of<DatabaseProvider>(context, listen: false)
            .addOrUpdateMatching(varUserInfo, tapType, message);
    //
    Provider.of<DatabaseProvider>(context, listen: false)
        .removeProfileFromAnyList(index, type);
    // now if similar then update profiel list
    if (type == 'similar') {
      Provider.of<DatabaseProvider>(context, listen: false)
          .updateSimilarProfileList(varUserInfo.userId);
    }
    //
    setState(() {
      _isLoading = false;
    });

    Navigator.of(context).pop();
    //
    if (varChatDetails != null) {
      Navigator.of(context).pushNamed(MatchScreen.routeName,
          arguments: [varUserInfo, varChatDetails]);
    }
    //
  }

  ////
  Future<void> _showMessageDialog(BuildContext context, int tapType,
      MyUserInfo varUserInfo, index, type) async {
    TextEditingController _messageController = TextEditingController();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent, // Make the background transparent
      builder: (BuildContext context) {
        return Container(
          decoration: BoxDecoration(
            color: mainColor,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(5.w),
              topRight: Radius.circular(5.w),
            ),
            boxShadow: [
              BoxShadow(
                color: mainContrastColor.withOpacity(0.2),
                blurRadius: 2.w,
                spreadRadius: 1.w,
              ),
            ],
          ),
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
            left: 5.w,
            right: 5.w,
            top: 5.h,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(height: 2.h),
              // Text Field with Modern Design
              Container(
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(1.h),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 2.h),
                  child: TextField(
                    inputFormatters: <TextInputFormatter>[
                      LengthLimitingTextInputFormatter(100),
                    ],
                    controller: _messageController,
                    decoration: InputDecoration(
                      hintText: 'Leave a message :)',
                      border: InputBorder.none,
                      hintStyle: TextStyle(color: Colors.grey[500]),
                    ),
                    maxLines: 3,
                    style: TextStyle(color: Colors.black87),
                  ),
                ),
              ),
              SizedBox(height: 2.h),
              // Gradient Send Button
              Container(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () async {
                    String message = _messageController.text.trim();
                    if (message.length >= 1) {
                      Navigator.pop(context); // Close the dialog
                      await onTapFunc(
                          tapType, varUserInfo, index, type, message);
                    } else {
                      callSnackfoldTop(context,
                          'Message must be at least 1 characters long.');
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    // primary: Colors.transparent,
                    shadowColor: Colors.transparent,
                    backgroundColor: rightSwipeButtonColor,
                    padding: EdgeInsets.symmetric(vertical: 1.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(1.h),
                    ),
                  ),
                  child: Text(
                    'Send Message',
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.bold,
                      color: mainColor,
                    ),
                  ),
                ),
              ),
              SizedBox(height: 2.h),
            ],
          ),
        );
      },
    );
  }

  // Function to handle sending the message
  void _sendMessage(String message) async {}

  @override
  Widget build(BuildContext context) {
    //////////
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('EachSimilarScreen', {});
      _isAnalytics = false;
    }
    ////////////
    final screenPadding = 1.w;
    double availableHeight = 100.h;
    final double screenWidth = 100.w - 2 * screenPadding;
    final double childWidth =
        screenWidth / 2; // divide by the number of columns
    final eachPhotoHeight = (availableHeight / 3);
    final double childAspectRatio = childWidth / eachPhotoHeight;
    // print(childAspectRatio);

    //
    final args = ModalRoute.of(context)!.settings.arguments as List;
    MyUserInfo varUserInfo = args[0];
    int index = args[1];
    _photos = args[2];
    String type = args[3];
    //
    final _myUserInfo =
        Provider.of<DatabaseProvider>(context, listen: true).getMyUserInfo;
    final _myUserPrefs =
        Provider.of<DatabaseProvider>(context, listen: true).getMyUserPrefs;
    // userinfo
    int age = findAge(varUserInfo.dob);
    int distance = distanceBetweenGeoLocator(
        _myUserInfo.latitude,
        _myUserInfo.longitude,
        varUserInfo.latitude,
        varUserInfo.longitude,
        _myUserPrefs);
    if (_photos.contains('isLoading')) {
      _isLoading = true;
      // print("isLoading");
    } else {
      _isLoading = false;
      // print("Loading done");
    }
    ////
    Map<int, int> _userInfo2Params =
        Provider.of<DatabaseProvider>(context, listen: true).getUserInfo2Params;

    ///
    final _promptList =
        Provider.of<DatabaseProvider>(context, listen: true).getPromptList;
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
      ),
      body: _isLoading
          ? LoadingWidget()
          : Stack(
              children: [
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        mainColor,
                        // mainColor,

                        mainContrastColor.withOpacity(0.1),
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(0, 8.h, 0, 0),
                  child: Container(
                    // height: 300, // Adjust height as per your content
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        SingleChildScrollView(
                          controller: _scrollController,
                          child: AnimatedSwitcher(
                            duration: const Duration(
                                milliseconds: 300), // Smoother animation
                            transitionBuilder: (child, animation) {
                              return FadeTransition(
                                opacity: animation,
                                child: ScaleTransition(
                                  scale: animation,
                                  child: child,
                                ),
                              );
                            },
                            child: Padding(
                              padding: EdgeInsets.all(
                                  4.w), // Increased padding for better spacing
                              child: CompleteUserInfoDisplay(
                                photoLink: _photos,
                                varUserInfo: varUserInfo!,
                                age: age,
                                distance: distance,
                                column2Show: true,
                                resizeFactor: 1,
                                promptList: _promptList!,
                              ),
                            ),
                          ),
                        ),
                        InterestTab(
                          myUserInfo: varUserInfo!,
                          isEditActive: false,
                        ),
                        PersonalityTab(
                          userInfo: varUserInfo!,
                          isEditActive: false,
                        ),
                      ],
                    ),
                  ),
                ),
                // SingleChildScrollView(
                //   child: AnimatedSwitcher(
                //     duration:
                //         const Duration(milliseconds: 300), // Smoother animation
                //     transitionBuilder: (child, animation) {
                //       return FadeTransition(
                //         opacity: animation,
                //         child: ScaleTransition(
                //           scale: animation,
                //           child: child,
                //         ),
                //       );
                //     },
                //     child: Padding(
                //       padding: EdgeInsets.all(2.w),
                //       child: CompleteUserInfoDisplay(
                //         photoLink: _photos,
                //         varUserInfo: varUserInfo,
                //         age: age,
                //         distance: distance,
                //         column2Show: true,
                //         resizeFactor: 1,
                //         promptList: _promptList,
                //       ),
                //     ),
                //   ),
                // ),
                Positioned(
                  bottom: 3.h,
                  left: 0,
                  right: 0,
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 4.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        GestureDetector(
                          onTap: () async {
                            await onTapFunc(-1, varUserInfo, index, type, "");
                          },
                          child: const LeftSwipeButton(),
                        ),
                        GestureDetector(
                          onTap: () async {
                            // if (_userInfo2Params[1]! <= 0 &&
                            //     _myUserInfo.isMembership == false) {
                            if (_userInfo2Params[1]! <= 0) {
                              callToast(
                                  LocaleKeys.SwipeScreen_No_likes_left.tr());
                              return;
                            }

                            // await onTapFunc(1, varUserInfo, index, type);
                            await _showMessageDialog(
                                context, 1, varUserInfo, index, type);
                          },
                          child: const RightSwipeButton(),
                        ),
                      ],
                    ),
                  ),
                ),
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: Align(
                    alignment: Alignment.topCenter,
                    child: Padding(
                      padding: EdgeInsets.fromLTRB(0, 1.h, 0, 2.h),
                      child: Container(
                        width: 80.w,
                        height: 5.h,
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [mainContrastColor, thirdColor]),
                          borderRadius: BorderRadius.circular(
                              5.w), // Adjust for capsule shape
                        ),
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                              vertical: 2.w, horizontal: 1.w),
                          child: TabBar(
                            dividerColor: Colors.transparent,
                            controller: _tabController,
                            unselectedLabelColor:
                                mainColor, // Set text color for unselected tabs
                            labelColor:
                                Colors.white, // Set text color for selected tab
                            indicatorColor:
                                Colors.transparent, // Hide default indicator
                            // indicatorSize: TabBarIndicatorSize.tab,
                            tabs: [
                              Tab(
                                  child: Center(
                                      child: Text("Looks",
                                          style: TextStyle(fontSize: 9.sp)))),
                              Tab(
                                  child: Center(
                                      child: Text("Interest",
                                          style: TextStyle(fontSize: 9.sp)))),
                              Tab(
                                  child: Center(
                                      child: Text("Personality",
                                          style: TextStyle(fontSize: 9.sp)))),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                )
              ],
            ),
    );
  }
}
