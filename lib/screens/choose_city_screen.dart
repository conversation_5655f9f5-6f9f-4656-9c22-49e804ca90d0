import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:zupid/models/myUserInfo.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/common_things/my_constants.dart';
import 'package:zupid/providers/database_provider.dart';
import 'package:zupid/widgets/loading_widget.dart';

import '../common_things/analytics.dart';
import '../common_things/my_colors.dart';

class ChooseCityScreen extends StatefulWidget {
  static const routeName = '/choose-city-screen';
  @override
  State<ChooseCityScreen> createState() => _ChooseCityScreenState();
}

class _ChooseCityScreenState extends State<ChooseCityScreen> {
  /////
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  //////
  bool _isLoading = false;
  bool isFirst = true;
  final TextEditingController _typeAheadController = TextEditingController();

  /// Variables to store country state city data in onChanged method.
  String countryValue = "";
  String stateValue = "";
  String cityValue = "";
  String address = "";
  // GlobalKey<CSCPickerState> _cscPickerKey = GlobalKey();
  //
  MyUserInfo? myUserInfo;
  @override
  Widget build(BuildContext context) {
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('ChooseCityScreen', {});
      _isAnalytics = false;
    }
    if (isFirst) {
      myUserInfo =
          Provider.of<DatabaseProvider>(context, listen: false).getMyUserInfo;
      cityValue = myUserInfo!.cityName;
      isFirst = false;
    }

    return Scaffold(
      appBar: AppBar(
        backgroundColor: mainColor,
        title: Text(
          LocaleKeys.ChooseCityScreen_Select_your_city.tr(),
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
      ),
      body: WillPopScope(
        onWillPop: (() async {
          setState(() {
            _isLoading = true;
          });
          //
          Map<String, String> _payloadData = {
            'city_name': cityValue,
            'state_name': stateValue,
            'country_name': countryValue,
          };
          await Provider.of<DatabaseProvider>(context, listen: false)
              .updateUserInfo(
                  userInfoCityStateCountryUpdateRoute, _payloadData);
          //
          setState(() {
            _isLoading = false;
          });
          return true;
        }),
        child: _isLoading
            ? LoadingWidget()
            : SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.all(10.w),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        cityValue,
                        style: TextStyle(
                            fontSize: 13.sp,
                            fontWeight: FontWeight.w600,
                            color: mainContrastColor),
                      ),
                      SizedBox(height: 3.h),
                      if (cityValue != "")
                        Column(
                          children: [
                            SizedBox(
                              height: 3.h,
                            ),
                            TypeAheadField(
                              textFieldConfiguration: TextFieldConfiguration(
                                inputFormatters: [
                                  FilteringTextInputFormatter.allow(
                                      RegExp("[a-zA-Z]")),
                                ],
                                controller: _typeAheadController,
                                decoration: InputDecoration(
                                  filled: true,
                                  fillColor:
                                      mainColor, // Light background for modern look
                                  border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(
                                          12.sp), // Rounded corners
                                      borderSide:
                                          BorderSide(color: mainContrastColor)),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12.sp),
                                    borderSide:
                                        BorderSide(color: mainContrastColor),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12.sp),
                                    borderSide: BorderSide(
                                      color:
                                          mainContrastColor, // Your main color for focus border
                                      width: 2.sp,
                                    ),
                                  ),
                                  label: Text(
                                    LocaleKeys.FirstTimeScreen_city.tr(),
                                    style: TextStyle(
                                      color: mainContrastColor,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  // labelText:
                                  //     ,
                                  prefixIcon: const Icon(Icons.search,
                                      color:
                                          mainContrastColor), // Custom color for the icon

                                  contentPadding: EdgeInsets.symmetric(
                                      vertical: 2.h, horizontal: 3.w),
                                  hintStyle: TextStyle(
                                      color: mainContrastColor,
                                      fontSize: 12.sp),
                                ),
                              ),
                              suggestionsCallback: (pattern) async {
                                return await Provider.of<DatabaseProvider>(
                                        context,
                                        listen: false)
                                    .searchQuery(pattern);
                              },
                              transitionBuilder:
                                  (context, suggestionsBox, controller) {
                                return suggestionsBox;
                              },
                              itemBuilder: (context, suggestion) {
                                cityValue = suggestion['city_name'];
                                stateValue = suggestion['state_name'];
                                countryValue = suggestion['country_name'];
                                return Container(
                                  color: mainColor,
                                  child: Column(
                                    children: [
                                      ListTile(
                                        onTap: () {
                                          _typeAheadController.clear();
                                          setState(() {
                                            cityValue = suggestion['city_name'];
                                            stateValue =
                                                suggestion['state_name'];
                                            countryValue =
                                                suggestion['country_name'];
                                          });
                                        },
                                        title: Text(
                                          '$cityValue, $stateValue',
                                          style: TextStyle(
                                              color: mainContrastColor,
                                              fontSize: 12.sp,
                                              fontWeight: FontWeight.bold),
                                        ),
                                        subtitle: Text(
                                          countryValue,
                                          style: TextStyle(
                                              color: mainContrastColor,
                                              fontSize: 10.sp),
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                              onSuggestionSelected: (suggestion) {
                                setState(() {});
                              },
                            ),
                            SizedBox(height: 5.h),
                          ],
                        ),
                    ],
                  ),
                ),
              ),
      ),
    );
  }
}
