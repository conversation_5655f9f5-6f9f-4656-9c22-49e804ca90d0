import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:zupid/common_things/images.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:zupid/widgets/my_blur_widget.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/models/myUserPrefs.dart';
import 'package:zupid/screens/each_similar_screen.dart';
import 'package:zupid/widgets/loading_widget.dart';

import '../common_things/analytics.dart';
import '../common_things/my_colors.dart';
import '../common_things/my_fucntions.dart';
import '../models/myUserInfo.dart';
import '../providers/database_provider.dart';
import '../widgets/my_stack_button.dart';

class LikedYouScreen extends StatefulWidget {
  static const routeName = '/liked-you-screen';

  @override
  State<LikedYouScreen> createState() => _LikedYouScreenState();
}

class _LikedYouScreenState extends State<LikedYouScreen> {
  /////
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  bool dataLoadedMapCondition = false;
  //////
  //   with AutomaticKeepAliveClientMixin {
  // /////////////////////////////
  // @override
  // bool get wantKeepAlive => true;
  //////////////////////////
  // List<String> _photos = [];
  List<MyUserInfo> _whoLikedYouList = [];
  //
  void initState() {
    super.initState();
  }

  // //
  Future<void> loadData() async {
    final results = await Provider.of<DatabaseProvider>(context, listen: false)
        .fetchWhoLikedYou();
  }

  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  void _onRefresh() async {
    // print('on refresh');
    // monitor network fetch
    await Future.delayed(Duration(milliseconds: 1000));
    await loadData();
    // if failed,use refreshFailed()
    _refreshController.refreshCompleted();
  }

  void _onLoading() async {
    // print('on loading');
    // monitor network fetch
    await Future.delayed(Duration(milliseconds: 1000));
    // if failed,use loadFailed(),if no data return,use LoadNodata()
    await loadData();
    // _bullaChats.add((items .length + 1).toString());
    if (mounted) setState(() {});
    _refreshController.loadComplete();
  }

  //
  bool? noMoreWhoLikedYouLeft;
  List<MyUserInfo>? whoLikedYouProfilesList;
  int? totalProfiles;
  MyUserInfo? _myUserInfo;
  MyUserPrefs? _myUserPrefs;
  int? _phoneNumber;
  /////
  ///
  @override
  Widget build(BuildContext context) {
    //////////
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('LikedYouScreen', {});
      _isAnalytics = false;
    }
    ////////////
    var childAspectRatio = 1.0;
    var perRow = 1;
    var containerMargin = 5.w;
    var eachHeight =
        ((100.w - 2 * perRow * containerMargin) / perRow) / childAspectRatio;
    //
    //  ////// data loaded condition
    final _dataLoadedMap =
        Provider.of<DatabaseProvider>(context, listen: true).getDataLoadedMap;
    dataLoadedMapCondition = _dataLoadedMap['myUserInfo']! &&
        _dataLoadedMap['myUserPrefs']! &&
        _dataLoadedMap['whoLikedYouScreen']!;
    dataLoadedMapCondition = _dataLoadedMap['loadall']!;

    ///
    if (dataLoadedMapCondition) {
      noMoreWhoLikedYouLeft =
          Provider.of<DatabaseProvider>(context, listen: true)
              .getNoWhoLikedYouProfilesLeft;
      whoLikedYouProfilesList =
          Provider.of<DatabaseProvider>(context, listen: true)
              .getWhoLikedYouProfilesList;
      //
      totalProfiles = whoLikedYouProfilesList!.length;
      // print("total profiles: $totalProfiles");
      //
      _myUserInfo =
          Provider.of<DatabaseProvider>(context, listen: true).getMyUserInfo;
      _myUserPrefs =
          Provider.of<DatabaseProvider>(context, listen: true).getMyUserPrefs;
      _phoneNumber =
          Provider.of<DatabaseProvider>(context, listen: true).getMyPhoneNumber;
    }

    //
    final tempImageDirectory =
        Provider.of<DatabaseProvider>(context, listen: false)
            .tempDirectoryForImage;

    return dataLoadedMapCondition == false
        ? LoadingWidget()
        : totalProfiles == 0
            ? Padding(
                padding: EdgeInsets.all(10.w),
                child: Center(
                    child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 2.h,
                    ),
                    Text(
                      'Likes You',

                      // LocaleKeys.LikedYouScreen_Who_Liked_You.tr(),
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 4.w,
                      ),
                    ),
                    SizedBox(
                      height: 20.h,
                    ),
                    Container(
                      alignment: Alignment.center,
                      height: 10.h,
                      // width: 25.w,
                      child: Image.asset(
                        MyImages.kRose,
                      ),
                    ),
                    SizedBox(
                      height: 5.h,
                    ),
                    Text(
                      "No profiles have liked you yet",
                      style: TextStyle(
                          fontSize: 12.sp, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(
                      height: 2.h,
                    ),
                    Text(
                      'Try sending more messages and updating your preferences to get noticed!',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 10.sp,
                      ),
                    ),
                  ],
                )),
              )
            : SmartRefresher(
                enablePullDown: false,
                enablePullUp: true,
                header: WaterDropHeader(),
                footer: CustomFooter(
                  builder: (BuildContext context1, LoadStatus? mode) {
                    Widget body;
                    // if (totalProfiles == _bullaChats.length) {
                    //   body = Text(
                    //     "No more opinion to load",
                    //     style: TextStyle(fontSize: 10.sp, color: Colors.grey),
                    //   );
                    // }
                    if (mode == LoadStatus.loading) {
                      body = LoadingWidget();
                    } else if (mode == LoadStatus.failed) {
                      body = Text(
                        LocaleKeys.Common_Load_Failed_Click_retry.tr(),
                        style: TextStyle(
                          fontSize: 10.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      );
                    } else if (mode == LoadStatus.canLoading) {
                      body = Text(
                        "release to load more",
                        style: TextStyle(
                          fontSize: 10.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      );
                    } else {
                      body = Text(
                        LocaleKeys.LikedYouScreen_No_more_Profiles.tr(),
                        style: TextStyle(
                          fontSize: 10.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      );
                    }
                    return Container(
                      height: 55.0,
                      child: Center(child: body),
                    );
                  },
                ),
                controller: _refreshController,
                onRefresh: _onRefresh,
                onLoading: _onLoading,
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: 2.h,
                      ),
                      Text(
                        'Likes You',
                        // LocaleKeys.LikedYouScreen_Who_Liked_You.tr(),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 4.w,
                        ),
                      ),
                      SizedBox(
                        height: 3.h,
                      ),
                      Container(
                        color: mainColor,
                        // height: availableHeight,
                        child: GridView.count(
                          crossAxisCount: perRow,
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          padding: EdgeInsets.all(0.w),
                          childAspectRatio: childAspectRatio,
                          children: List.generate(totalProfiles!, (index) {
                            MyUserInfo varUserInfo =
                                whoLikedYouProfilesList![index];
                            // Safety check: Ensure photos map exists before accessing
                            final photosMap = Provider.of<DatabaseProvider>(
                                    context,
                                    listen: true)
                                .getUserSignedUrlMap;

                            List<String> _photos;
                            if (photosMap.containsKey(varUserInfo.userId)) {
                              _photos = photosMap[varUserInfo.userId]!;
                            } else {
                              // If photos not loaded yet, show loading state
                              _photos = [
                                "isLoading",
                                "isLoading",
                                "isLoading",
                                "isLoading",
                                "isLoading",
                                "isLoading"
                              ];

                              // Trigger loading for this profile
                              Provider.of<DatabaseProvider>(context,
                                      listen: false)
                                  .fetchAllImages(varUserInfo);
                            }
                            Map<String, String> whoLikedYouMessageMap =
                                Provider.of<DatabaseProvider>(context,
                                        listen: false)
                                    .getWhoLikedYouMessageMap;
                            return GestureDetector(
                              onTap: () {
                                Navigator.of(context).pushNamed(
                                  EachSimilarScreen.routeName,
                                  arguments: [
                                    varUserInfo,
                                    index,
                                    _photos,
                                    'liked'
                                  ],
                                );
                              },
                              child: Container(
                                margin: EdgeInsets.symmetric(
                                    vertical: 1.h, horizontal: 2.w),
                                decoration: BoxDecoration(
                                  color: mainContrastColor,
                                  borderRadius: BorderRadius.circular(2.h),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black,
                                      blurRadius: 8,
                                      offset: Offset(0, 4),
                                    ),
                                  ],
                                ),
                                clipBehavior: Clip.antiAlias,
                                child: Column(
                                  children: [
                                    // Quote Section
                                    Container(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 4.w, vertical: 1.5.h),
                                      color: mainColor.withOpacity(0.1),
                                      width: double.infinity,
                                      child: Text(
                                        "\"${whoLikedYouMessageMap[varUserInfo.userId] ?? ''}\"",
                                        style: TextStyle(
                                          fontSize: 12.sp,
                                          fontStyle: FontStyle.italic,
                                          fontWeight: FontWeight.w500,
                                          color: mainColor,
                                        ),
                                        textAlign: TextAlign.center,
                                        maxLines: 3,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),

                                    // Image with Name Overlay
                                    Expanded(
                                      child: Stack(
                                        children: [
                                          Positioned.fill(
                                            child: _photos[0] == "isLoading"
                                                ? LoadingWidget()
                                                : _photos.isNotEmpty &&
                                                        _photos[0] != "-1"
                                                    ? Image.file(
                                                        File(
                                                            tempImageDirectory +
                                                                _photos[0]),
                                                        fit: BoxFit.cover,
                                                        errorBuilder: (context,
                                                            error, stackTrace) {
                                                          // Show default no-photo if file loading fails
                                                          return Image.asset(
                                                            MyImages.kNoPhoto,
                                                            fit: BoxFit.cover,
                                                          );
                                                        },
                                                      )
                                                    : Image.asset(
                                                        MyImages.kNoPhoto,
                                                        fit: BoxFit.cover,
                                                      ),
                                          ),
                                          // Name Overlay
                                          Positioned(
                                            left: 0,
                                            right: 0,
                                            bottom: 0,
                                            child: Container(
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: 5.w,
                                                  vertical: 1.5.h),
                                              decoration: BoxDecoration(
                                                gradient: LinearGradient(
                                                  colors: [
                                                    Colors.transparent,
                                                    Colors.black
                                                        .withOpacity(0.7),
                                                    Colors.black
                                                        .withOpacity(0.9),
                                                  ],
                                                  begin: Alignment.topCenter,
                                                  end: Alignment.bottomCenter,
                                                ),
                                              ),
                                              child: Text(
                                                varUserInfo.isHideName
                                                    ? showOnlyFirstLetter(
                                                        varUserInfo.firstName)
                                                    : capitalizeFirstLetter(
                                                        varUserInfo.firstName),
                                                style: TextStyle(
                                                  fontSize: 14.sp,
                                                  fontWeight: FontWeight.bold,
                                                  color: Color.fromRGBO(
                                                      255, 230, 255, 1),
                                                  shadows: [
                                                    Shadow(
                                                      color: Colors.black
                                                          .withOpacity(0.8),
                                                      blurRadius: 6,
                                                      offset: Offset(0, 2),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          }),
                        ),
                      ),
                    ],
                  ),
                ),
              );
  }
}
