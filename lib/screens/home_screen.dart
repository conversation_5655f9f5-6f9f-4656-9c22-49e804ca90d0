import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:zupid/common_things/images.dart';
import 'package:zupid/common_things/my_constants.dart';
import 'package:zupid/common_things/my_fucntions.dart';
import 'package:zupid/models/myInterest.dart';
import 'package:zupid/models/myPersonalityTraits.dart';
import 'package:zupid/models/myUserInfo.dart';
import 'package:zupid/providers/auth_provider.dart';
import 'package:zupid/screens/about_myself_screen.dart';
import 'package:zupid/screens/choose_height_screen.dart';
import 'package:zupid/screens/edit_movie_screen.dart';
import 'package:zupid/screens/first_time_details_screen.dart';
import 'package:zupid/screens/first_time_info_screen.dart';
import 'package:zupid/screens/force_prompt_screen.dart';
import 'package:zupid/screens/personality_trait_selection_screen.dart';
import 'package:zupid/screens/referral_entry_screen.dart';
import 'package:zupid/screens/select_category_interest_screen.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:zupid/widgets/personality_tab.dart';
import 'package:zupid/widgets/photos_container.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/providers/database_provider.dart';
import 'package:zupid/screens/account_ban_screen.dart';
import 'package:zupid/screens/similar_screen.dart';
import 'package:zupid/screens/startup_screen.dart';
import 'package:zupid/screens/swipe_screen.dart';
import 'package:zupid/widgets/loading_widget.dart';
import 'package:upgrader/upgrader.dart';

import '../common_things/analytics.dart';
import 'Profile_screen.dart';
import 'chat_screen.dart';
import 'liked_you_screen.dart';

class HomeScreen extends StatefulWidget {
  static const routeName = '/home_screen';
  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  /////
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  //////
  final _formKey = GlobalKey<FormState>();

  // AppUpdateInfo? myAppUpdateInfo;
  TextEditingController _textEditingController = TextEditingController();
  bool _isLoading = true;
  bool appUpdateRunOnce = false;
  bool isBlocked = false;
  bool isAllPhotosThere = false;
  bool isReferralFirst = false;
  String promoCode = "";
  //// newadded /////
  bool isMovieCheck = false;
  MyUserInfo? _myUserInfo;
  bool isPersonalInfoDone = false;

  /// new added end ////
  // AppUpdateInfo? myAppUpdateInfo;
  int selectedTabIndex = 0;
  int pendingCategoryIndex = 0;
  final List<Map<String, Object>> _pages = [
    {'page': SwipeScreen()},
    {'page': SimilarScreen()},
    {'page': LikedYouScreen()},
    {'page': ChatScreen()},
    {'page': ProfileScreen()},
  ];
  void _selectedPage(int index) {
    setState(() {
      selectedTabIndex = index;
    });
  }

  //alert dialog
  // void showAlert(BuildContext context) {
  //   showDialog(
  //     context: context,
  //     builder: (_) => AlertDialog(
  //       alignment: Alignment.center,
  //       title: Text(
  //         'Enjoy new features',
  //         style: TextStyle(color: Colors.green),
  //       ),
  //       content: Text(
  //         'Update App to the latest Version',
  //       ),
  //       actions: [
  //         TextButton(
  //           onPressed: () {
  //             //////// For apk file ////////////
  //             // Navigator.of(context).pushNamed(DownloadAppScreen.routeName);
  //             /////////////////////////////////////
  //             // for google play store ///////////
  //             // Navigator.pop(context);
  //             if (myAppUpdateInfo!.updateAvailability ==
  //                 UpdateAvailability.updateAvailable) {
  //               InAppUpdate.performImmediateUpdate()
  //                   .catchError((e) => callToast(e.toString()));
  //             } else {
  //               callToast(
  //                   'Please update in few hours or clean Playstore cache to update now');
  //             }
  //           },
  //           child: const Text(
  //             'Update Now',
  //             style: TextStyle(color: Colors.green),
  //           ),
  //         ),
  //         TextButton(
  //           onPressed: () {
  //             Navigator.pop(context);
  //           },
  //           child: Text(
  //             'Later',
  //             style: TextStyle(color: Colors.black),
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }
  late List<MyInterest> _allInterests;
  List<String> _selectedInterests = [];

  // void _fetchInterests() {

  // }

  bool categoryMinimumFailed(String selectedCategory) {
    final databaseProvider =
        Provider.of<DatabaseProvider>(context, listen: true);
    _allInterests = databaseProvider.getInterestList;
    _selectedInterests = databaseProvider.getMyUserInfo.allInterests.interests;
    int selectedCount = _selectedInterests
        .where((id) =>
            _allInterests
                .firstWhere((interest) => interest.id == id)
                .category ==
            selectedCategory)
        .length;
    return selectedCount < 1 ? true : false;
  }

  // Function to check for missing personality traits
  bool hasMissingPersonalityTraits(MyUserInfo userInfo) {
    final userTraits = userInfo.allInterests.personalityTraitsJson;
    final allTraitIds = Provider.of<DatabaseProvider>(context, listen: false)
        .getAllPersonalityTraitsList
        .map((trait) => trait.id)
        .toList();

    // Check if the user has filled out all traits
    for (var traitId in allTraitIds) {
      if (!userTraits.any((trait) => trait.traitId == traitId)) {
        return true; // Missing trait found
      }
    }
    return false; // All traits are filled out
  }

  // load token and fetch userinfo in database provider //
  Future<void> loadData() async {
    // print('loading data for database provider');
    final myUserId = await Provider.of<DatabaseProvider>(context, listen: false)
        .fetchAuthTokenAndUserId();
    Provider.of<MyAnalytics>(context, listen: false).setUserId(myUserId);
    await Provider.of<DatabaseProvider>(context, listen: false)
        .LoadAllRequest();
    // print('test-1');
    //

    // Provider.of<DatabaseProvider>(context, listen: false).fetchAllInterests();
    // Provider.of<DatabaseProvider>(context, listen: false).fetchAllPromptList();
    // await Provider.of<DatabaseProvider>(context, listen: false)
    //     .fetchMyUserInfo();
    // // await Provider.of<AuthProvider>(context, listen: false).checkForAppUpdate();
    // // myAppUpdateInfo =
    // //     Provider.of<AuthProvider>(context, listen: false).myAppUpdateInfo!;
    // // to check update
    // //
    // Provider.of<DatabaseProvider>(context, listen: false).fetchMyUserInfo2();
    // Provider.of<DatabaseProvider>(context, listen: false).fetchMyUserPrefs();
    // Provider.of<DatabaseProvider>(context, listen: false)
    //     .fetchSwipeScreenProfiles();
    // Provider.of<DatabaseProvider>(context, listen: false).fetchWhoLikedYou();
    // Provider.of<DatabaseProvider>(context, listen: false).fetchSimilarProfile();
    // Provider.of<DatabaseProvider>(context, listen: false)
    //     .fetchAllMatchedProfiles();
    // Provider.of<DatabaseProvider>(context, listen: false).fetchVerifyProfile();
    // Provider.of<DatabaseProvider>(context, listen: false)
    //     .updateUserInfoLastSeen();
    Provider.of<DatabaseProvider>(context, listen: false).updateFCMDatabase();
    // need this for block or not check //
    // print('isblocked: $isBlocked');
    isBlocked = Provider.of<DatabaseProvider>(context, listen: false).isBlocked;
    // print('isblocked: $isBlocked');
    if (isBlocked) {
      setState(() {
        _isLoading = false;
      });
      return;
    }
    Provider.of<DatabaseProvider>(context, listen: false).listenToSSE();
    // print('loaded data for db provider');
    setState(() {
      _isLoading = false;
    });
  }

  // checkInterestCategory(String categoryName, MyUserInfo _myUserInfo) {
  //   final interestMinMap =
  //       Provider.of<AuthProvider>(context, listen: false).getInterestsMinimum;
  //   int minimum = interestMinMap[categoryName];
  //   //

  //   if (categoryName == 'movies') {
  //     return _myUserInfo.allInterests.movies.length >= minimum;
  //   } else {
  //     final databaseProvider =
  //         Provider.of<DatabaseProvider>(context, listen: false);
  //     final _allInterests = databaseProvider.getInterestList;
  //     final _selectedInterests =
  //         databaseProvider.getMyUserInfo.allInterests.interests;
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    //////////
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('HomeScreen', {});
      _isAnalytics = false;
    }
    ////////////
    // alert dialog app update
    // context.setLocale(
    //   Locale(
    //       Provider.of<AuthProvider>(context, listen: false).getCurrentLanguage),
    // );

    if (appUpdateRunOnce == false) {
      // final appVersion = Provider.of<AuthProvider>(context, listen: false)
      //     .deviceAndLatestVersionAndBuild;
      // if (appVersion[0] != appVersion[2]) {
      //   // print('check: ${appVersion[0]}${appVersion[2]}');
      //   Future.delayed(Duration.zero, () => showAlert(context));
      // }
      appUpdateRunOnce = true;
      loadData();
    }

    // till now used auth provider, now working start in database provider //
    if (!_isLoading) {
      isAllPhotosThere = Provider.of<DatabaseProvider>(context, listen: true)
          .areAllPhotosPresent;
      isReferralFirst = Provider.of<DatabaseProvider>(context, listen: true)
          .getMyReferral
          .isFirst;
      isMovieCheck =
          Provider.of<DatabaseProvider>(context, listen: true).isMovieCheck;
      _myUserInfo =
          Provider.of<DatabaseProvider>(context, listen: true).getMyUserInfo;
      final _promptList =
          Provider.of<DatabaseProvider>(context, listen: false).getPromptList;
      // print("is Two photos : $isTwoPhotos");
    }
    //
    // String appcastURL = 'https://www.zupid.com/upgrader.xml';
    // final upgrader = Upgrader(
    //   storeController: UpgraderStoreController(
    //     onAndroid: () => UpgraderAppcastStore(appcastURL: appcastURL),
    //   ),
    // );
    // final appcastConfig = AppcastConfiguration(url: appcastURL, supportedOS: [
    //   'android',
    // ]);
    //
    isPersonalInfoDone =
        Provider.of<DatabaseProvider>(context, listen: true).isPersonalInfoDone;

    return _isLoading
        ? StartupScreen()
        : isBlocked
            ? Scaffold(
                body: AccountBanScreen(),
              )
            : isAllPhotosThere == false
                ? Scaffold(
                    body: Container(
                      height: 100.h,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            mainColor,
                            mainContrastColor.withOpacity(0.1),
                          ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                      ),
                      child: Padding(
                        padding: EdgeInsets.all(2.5.w),
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              const PhotosContainer(
                                showOnly2: false,
                              ),
                              SizedBox(height: 3.h),
                              Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 2.5.w, vertical: 0),
                                child: Text(
                                  LocaleKeys.HomeScreen_Upload_atleast_2_photos
                                      .tr(),
                                  style: TextStyle(
                                    fontSize: 10.sp,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  )
                : isReferralFirst && !isPersonalInfoDone
                    ? FirstTimeDetailsScreen()
                    : isReferralFirst
                        ? ReferralEntryScreen()
                        : !isMovieCheck
                            ? const EditMovieScreen()
                            : _myUserInfo!.height == 0
                                ? const ChooseHeightScreen(
                                    isHeightCheck: true,
                                  )
                                : _myUserInfo!.aboutMe.isEmpty
                                    ? AboutMyselfScreen()
                                    : categoryMinimumFailed(
                                            INTEREST_CATEGORIES[0])
                                        ? SelectCategoryInterestScreen(
                                            categoryName:
                                                INTEREST_CATEGORIES[0])
                                        : categoryMinimumFailed(
                                                INTEREST_CATEGORIES[1])
                                            ? SelectCategoryInterestScreen(
                                                categoryName:
                                                    INTEREST_CATEGORIES[1])
                                            : categoryMinimumFailed(
                                                    INTEREST_CATEGORIES[2])
                                                ? SelectCategoryInterestScreen(
                                                    categoryName:
                                                        INTEREST_CATEGORIES[2])
                                                : categoryMinimumFailed(
                                                        INTEREST_CATEGORIES[3])
                                                    ? SelectCategoryInterestScreen(
                                                        categoryName:
                                                            INTEREST_CATEGORIES[
                                                                3])
                                                    : categoryMinimumFailed(
                                                            INTEREST_CATEGORIES[
                                                                4])
                                                        ? SelectCategoryInterestScreen(
                                                            categoryName:
                                                                INTEREST_CATEGORIES[
                                                                    4])
                                                        : categoryMinimumFailed(
                                                                INTEREST_CATEGORIES[
                                                                    5])
                                                            ? SelectCategoryInterestScreen(
                                                                categoryName:
                                                                    INTEREST_CATEGORIES[
                                                                        5])
                                                            : categoryMinimumFailed(
                                                                    INTEREST_CATEGORIES[6])
                                                                ? SelectCategoryInterestScreen(categoryName: INTEREST_CATEGORIES[6])
                                                                : categoryMinimumFailed(INTEREST_CATEGORIES[7])
                                                                    ? SelectCategoryInterestScreen(categoryName: INTEREST_CATEGORIES[7])
                                                                    : categoryMinimumFailed(INTEREST_CATEGORIES[8])
                                                                        ? SelectCategoryInterestScreen(categoryName: INTEREST_CATEGORIES[8])
                                                                        : hasMissingPersonalityTraits(_myUserInfo!)
                                                                            ?
                                                                            // SelectCategoryInterestScreen(categoryName: INTEREST_CATEGORIES[8])
                                                                            // :
                                                                            PersonalityTraitSelectionScreen()
                                                                            : _myUserInfo!.allInterests.prompts.length < 3
                                                                                ? ForcePromptScreenScreen()
                                                                                : Scaffold(
                                                                                    // backgroundColor: Colors.white,
                                                                                    // drawer: MyAppDrawer(),
                                                                                    appBar: AppBar(
                                                                                      // backgroundColor:
                                                                                      //     Colors.white, // Matches the screen background
                                                                                      foregroundColor: Colors.transparent, // Make sure to match with your theme
                                                                                      shadowColor: Colors.transparent, // Prevents any shadow color showing
                                                                                      surfaceTintColor: Colors.white,
                                                                                      elevation: 0,
                                                                                      title: Center(
                                                                                        child: Image.asset(
                                                                                          MyImages.k_500_250,
                                                                                          height: AppBar().preferredSize.height * .8,
                                                                                        ),
                                                                                      ),
                                                                                    ),
                                                                                    body: IndexedStack(
                                                                                      index: selectedTabIndex,
                                                                                      children: [
                                                                                        SwipeScreen(),
                                                                                        SimilarScreen(),
                                                                                        LikedYouScreen(),
                                                                                        ChatScreen(),
                                                                                        ProfileScreen(),
                                                                                      ],
                                                                                      // child: Container(
                                                                                      //     child: _pages[selectedTabIndex]['page'] as Widget,
                                                                                      //   ),
                                                                                    ),
                                                                                    bottomNavigationBar: BottomNavigationBar(
                                                                                      // elevation: 0,
                                                                                      // backgroundColor: mainContrastColor,
                                                                                      type: BottomNavigationBarType.fixed,
                                                                                      onTap: _selectedPage,
                                                                                      currentIndex: selectedTabIndex,
                                                                                      selectedFontSize: 9.sp,
                                                                                      unselectedFontSize: 9.sp,
                                                                                      showSelectedLabels: false,
                                                                                      showUnselectedLabels: false,
                                                                                      items: const [
                                                                                        BottomNavigationBarItem(
                                                                                          icon: Icon(
                                                                                            Icons.search_rounded,
                                                                                          ),
                                                                                          label: 'swipe',
                                                                                        ),
                                                                                        BottomNavigationBarItem(icon: Icon(Icons.electric_bolt_rounded), label: 'similar'),
                                                                                        BottomNavigationBarItem(icon: Icon(Icons.star_half_rounded), label: 'likedYou'),
                                                                                        BottomNavigationBarItem(icon: Icon(Icons.chat_bubble_outline_rounded), label: 'Chat'),
                                                                                        BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
                                                                                      ],
                                                                                    ),
                                                                                  );
  }
}
