import 'package:flutter/material.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/models/myUserInfo.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/providers/database_provider.dart';
import '../models/myPromptList.dart';

class ChoosePromptScreen extends StatefulWidget {
  static const routeName = '/choose-prompt-screen';

  @override
  State<ChoosePromptScreen> createState() => _ChoosePromptScreenState();
}

class _ChoosePromptScreenState extends State<ChoosePromptScreen> {
  List<MyPrompt> _promptList = [];
  String? _selectedCategory;
  bool firstRun = true;
  InterestPrompt? selectedInterestPrompt;
  TextEditingController _answerController = TextEditingController();
  MyPrompt? selectedPrompt;
  int interestPromptIndex = -1;

  @override
  Widget build(BuildContext context) {
    if (firstRun) {
      _promptList =
          Provider.of<DatabaseProvider>(context, listen: false).getPromptList;
      final args = ModalRoute.of(context)!.settings.arguments as List;
      interestPromptIndex = args[0] - 1;
      final allInterestPrompts =
          Provider.of<DatabaseProvider>(context, listen: false)
              .getMyUserInfo
              .allInterests
              .prompts;
      firstRun = false;
      bool alreadyHave =
          allInterestPrompts.length > interestPromptIndex ? true : false;

      /// new code ///
      List<String> existingIds = [];
      // Get existing IDs if passed (2nd argument)
      if (ModalRoute.of(context)!.settings.arguments is List) {
        final args = ModalRoute.of(context)!.settings.arguments as List;
        if (args.length > 1) existingIds = args[1].cast<String>();
      }
      /////

      if (alreadyHave) {
        selectedInterestPrompt = allInterestPrompts[interestPromptIndex];
        _selectedCategory = _promptList
            .firstWhere(
              (prompt) => prompt.id == selectedInterestPrompt!.id,
            )
            .category;
        selectedPrompt = _promptList
            .firstWhere((prompt) => prompt.id == selectedInterestPrompt!.id);
        _answerController.text = selectedInterestPrompt!.value;
      } else {
        final availablePrompts = existingIds.isEmpty
            ? _promptList
            : _promptList.where((p) => !existingIds.contains(p.id)).toList();

        _selectedCategory = availablePrompts[0].category; // Using it here
        selectedPrompt = availablePrompts[0]; // And here
      }
    }
    return Scaffold(
      appBar: AppBar(
        title: Text('Select Prompt',
            style: TextStyle(fontWeight: FontWeight.bold)),
        centerTitle: true,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [mainColor, mainColor.withOpacity(0.7)],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.black.withOpacity(0.95), Colors.black],
          ),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 2.h, horizontal: 5.w),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                if (selectedPrompt != null)
                  _buildQuestionWithEdit(selectedPrompt!),
                if (selectedPrompt != null) _buildAnswerInput(selectedPrompt!),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildQuestionWithEdit(MyPrompt prompt) {
    return Container(
      margin: EdgeInsets.only(bottom: 3.h),
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: Colors.grey[900]!.withOpacity(0.7),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: thirdColor.withOpacity(0.3), width: 1),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              prompt.question,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: Colors.white,
                height: 1.4,
              ),
            ),
          ),
          IconButton(
            icon: Icon(Icons.edit, color: thirdColor, size: 20.sp),
            // In the edit button onPressed in _buildQuestionWithEdit
            onPressed: () {
              final userPrompts =
                  Provider.of<DatabaseProvider>(context, listen: false)
                      .getMyUserInfo
                      .allInterests
                      .prompts
                      .map((p) => p.id)
                      .toList();

              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => PromptSelectionScreen(
                    promptList: _promptList,
                    selectedPrompt: selectedPrompt!,
                    onPromptSelected: (prompt) {
                      setState(() {
                        if (prompt.id != selectedPrompt!.id) {
                          _answerController.clear();
                        }
                        selectedPrompt = prompt;
                      });
                    },
                    existingPromptIds: userPrompts, // Add this line
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAnswerInput(MyPrompt prompt) {
    final _formKey = GlobalKey<FormState>();
    return Form(
      key: _formKey,
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.4),
                  blurRadius: 10,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: TextFormField(
              controller: _answerController,
              maxLines: 8,
              style: TextStyle(color: Colors.white, fontSize: 12.sp),
              decoration: InputDecoration(
                hintText: 'Enter your answer...',
                hintStyle: TextStyle(color: Colors.grey[500]),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(15),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[900]!.withOpacity(0.7),
                contentPadding: EdgeInsets.all(15),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your answer';
                }
                return null;
              },
            ),
          ),
          SizedBox(height: 4.h),
          Container(
            width: double.infinity,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: thirdColor,
                padding: EdgeInsets.symmetric(vertical: 2.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 5,
                shadowColor: thirdColor.withOpacity(0.4),
              ),
              onPressed: () {
                if (_formKey.currentState!.validate()) {
                  _formKey.currentState!.save();
                  Provider.of<DatabaseProvider>(context, listen: false)
                      .UpdatePrompts(interestPromptIndex, selectedPrompt!.id,
                          _answerController.text);
                  Navigator.pop(context);
                }
              },
              child: Text(
                'SUBMIT',
                style: TextStyle(
                  color: mainColor,
                  fontSize: 12.sp,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 1.2,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class PromptSelectionScreen extends StatefulWidget {
  final List<MyPrompt> promptList;
  final Function(MyPrompt) onPromptSelected;
  final MyPrompt selectedPrompt;
  final List<String> existingPromptIds; // Add this line

  const PromptSelectionScreen({
    Key? key,
    required this.promptList,
    required this.onPromptSelected,
    required this.selectedPrompt,
    required this.existingPromptIds, // Add this line
  }) : super(key: key);

  @override
  _PromptSelectionScreenState createState() => _PromptSelectionScreenState();
}

class _PromptSelectionScreenState extends State<PromptSelectionScreen> {
  String? _selectedCategory;
  List<String> _categories = [];
  String _selectedPromptId = '';

  @override
  void initState() {
    super.initState();
    _categories = widget.promptList.map((e) => e.category).toSet().toList();
    _selectedCategory = widget.selectedPrompt.category;
    _selectedPromptId = widget.selectedPrompt.id;
  }

  @override
  Widget build(BuildContext context) {
    List<MyPrompt> filteredPrompts = widget.promptList
        .where((p) => p.category == _selectedCategory)
        .where((p) =>
            !widget.existingPromptIds.contains(p.id) ||
            p.id == widget.selectedPrompt.id)
        .toList();

    return Scaffold(
      appBar: AppBar(
        title: Text('Select Question',
            style: TextStyle(fontWeight: FontWeight.bold)),
        centerTitle: true,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [mainColor, mainColor.withOpacity(0.7)],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.black.withOpacity(0.95), Colors.black],
          ),
        ),
        padding: EdgeInsets.all(3.w),
        child: Column(
          children: [
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: _categories.map((category) {
                  return Container(
                    margin: EdgeInsets.only(right: 2.w),
                    child: ChoiceChip(
                      label: Text(
                        category,
                        style: TextStyle(
                          fontSize: 11.sp,
                          color: _selectedCategory == category
                              ? mainColor
                              : mainContrastColor,
                        ),
                      ),
                      backgroundColor: Colors.grey[900],
                      selectedColor: thirdColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      selected: _selectedCategory == category,
                      onSelected: (selected) {
                        setState(() {
                          _selectedCategory = selected ? category : null;
                        });
                      },
                    ),
                  );
                }).toList(),
              ),
            ),
            SizedBox(height: 3.h),
            Expanded(
              child: ListView.separated(
                itemCount: filteredPrompts.length,
                separatorBuilder: (context, index) => Divider(
                  color: Colors.grey[800],
                  height: 1,
                ),
                itemBuilder: (context, index) {
                  final prompt = filteredPrompts[index];
                  return ListTile(
                    contentPadding:
                        EdgeInsets.symmetric(vertical: 1.h, horizontal: 3.w),
                    tileColor: _selectedPromptId == prompt.id
                        ? thirdColor.withOpacity(0.2)
                        : Colors.transparent,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    title: Text(
                      prompt.question,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: mainContrastColor,
                        fontWeight: _selectedPromptId == prompt.id
                            ? FontWeight.bold
                            : FontWeight.normal,
                      ),
                    ),
                    onTap: () {
                      widget.onPromptSelected(filteredPrompts[index]);
                      Navigator.pop(context);
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
