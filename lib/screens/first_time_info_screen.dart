
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:zupid/common_things/my_fucntions.dart';
import 'package:zupid/providers/database_provider.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/providers/auth_provider.dart';
import 'package:zupid/widgets/loading_widget.dart';
import 'package:toggle_switch/toggle_switch.dart';

import '../common_things/analytics.dart';

class FirstTimeInfoScreen extends StatefulWidget {
  static const routeName = '/first-time-info-screen';

  @override
  State<FirstTimeInfoScreen> createState() => _FirstTimeInfoScreenState();
}

class _FirstTimeInfoScreenState extends State<FirstTimeInfoScreen> {
  static const String screenName = "FirstTimeScreen";
  /////
  bool _isAnalytics = true;
  late final MyAnalytics myAnalytics;
  final TextEditingController _typeAheadController = TextEditingController();
  //////
  bool _isLoading = false;
  final _referralKey = GlobalKey<FormState>();
  Map<String, dynamic> _newUserInfo = {
    'first_name': '',
    'last_name': '',
    'dob': '',
    'sex': 0,
    // 'latitude': 0.0,
    // 'longitude': 0.0,
    'city_name': '',
    'state_name': '',
    'country_name': '',
    'user_prefs_sex': 1,
  };
  //
  int totalTask = 6;
  int currenTask = 1;
  TextEditingController _dateController = TextEditingController();
  DateTime _selectedDate = DateTime.now();
  bool isDateClickedOnce = false;
  //

  @override
  Widget build(BuildContext context) {
    print('FirstTimeScreen');
    //////////
    if (_isAnalytics) {
      myAnalytics = Provider.of<MyAnalytics>(context, listen: false);
      myAnalytics.setLogEvent('FirstTimeInfoScreen', {});
      _isAnalytics = false;
    }
    ////////////
    return Scaffold(
      body: _isLoading
          ? LoadingWidget()
          : Container(
              height: 100.h,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    mainColor,
                    mainContrastColor.withOpacity(0.1),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
              child: SafeArea(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 5.w),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 5.h),
                        Form(
                          key: _referralKey,
                          child: _buildCurrentStep(),
                        ),
                        SizedBox(height: 4.h),
                        Center(
                          child: Container(
                            width: 60.w,
                            child: ElevatedButton(
                              style: ButtonStyle(
                                backgroundColor:
                                    MaterialStateProperty.all(thirdColor),
                                padding: MaterialStateProperty.all(
                                  EdgeInsets.symmetric(vertical: 1.5.h),
                                ),
                                shape: MaterialStateProperty.all(
                                  RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(30),
                                  ),
                                ),
                              ),
                              onPressed: () => _handleNext(),
                              child: Text(
                                LocaleKeys.FirstTimeScreen_next.tr(),
                                style: TextStyle(
                                  color: mainColor,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12.sp,
                                ),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: 2.h),
                      ],
                    ),
                  ),
                ),
              ),
            ),
    );
  }

  String _getCurrentStepTitle() {
    switch (currenTask) {
      case 1:
        return LocaleKeys.FirstTimeScreen_first_name.tr();
      case 2:
        return LocaleKeys.FirstTimeScreen_last_name.tr();
      case 3:
        return LocaleKeys.FirstTimeScreen_sex.tr();
      case 4:
        return LocaleKeys.FirstTimeScreen_dob.tr();
      case 5:
        return LocaleKeys.FirstTimeScreen_city.tr();
      default:
        return "Basic Information";
    }
  }

  Widget _buildCurrentStep() {
    switch (currenTask) {
      case 1:
        return _buildFirstNameField();
      case 2:
        return _buildLastNameField();
      case 3:
        return _buildGenderSelection();
      case 4:
        return _buildDateOfBirthField();
      case 5:
        return _buildCitySelection();
      case 6:
        return _buildUserPrefsSelection();

      default:
        return Container();
    }
  }

  Widget _buildFirstNameField() {
    return Card(
      color: Colors.transparent,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
      ),
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: mainContrastColor, size: 20.sp),
                SizedBox(width: 3.w),
                Text(
                  LocaleKeys.FirstTimeScreen_first_name.tr(),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: mainContrastColor,
                    fontSize: 14.sp,
                  ),
                ),
              ],
            ),
            SizedBox(height: 3.h),
            TextFormField(
              style: TextStyle(fontWeight: FontWeight.bold),
              decoration: InputDecoration(
                filled: true,
                fillColor: mainColor,
                contentPadding:
                    EdgeInsets.symmetric(vertical: 2.h, horizontal: 3.w),
                hintStyle: TextStyle(color: Colors.grey, fontSize: 12.sp),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide(color: Colors.grey.withOpacity(0.3)),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide(color: Colors.grey.withOpacity(0.3)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide(color: thirdColor, width: 2),
                ),
              ),
              inputFormatters: [
                LengthLimitingTextInputFormatter(32),
                FilteringTextInputFormatter.allow(
                    RegExp("[a-zA-Z\u0900-\u097F]")),
              ],
              enabled: true,
              validator: (value) {
                if (value!.isEmpty) {
                  return LocaleKeys.Common_please_enter_a_value.tr();
                }
                //
                final RegExp regExp = RegExp(r'^[a-zA-Z\u0900-\u097F]+$');
                if (!regExp.hasMatch(value)) {
                  return LocaleKeys.Common_please_enter_only_alphabets.tr();
                }
                //
                if (value.length > 32) {
                  return '${LocaleKeys.Common_maximum_allowed_length_is.tr()} 32';
                }
                return null;
              },
              onSaved: (value) {
                _newUserInfo['first_name'] = value!;
              },
            ),
            SizedBox(height: 2.h),
            Text(
              LocaleKeys.FirstTimeScreen_as_mentioned_in_your_identity_card
                  .tr(),
              style: TextStyle(
                fontSize: 10.sp,
                color: Colors.grey,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLastNameField() {
    return Card(
      color: Colors.transparent,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
      ),
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person_outline,
                    color: mainContrastColor, size: 20.sp),
                SizedBox(width: 3.w),
                Text(
                  LocaleKeys.FirstTimeScreen_last_name.tr(),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: mainContrastColor,
                    fontSize: 14.sp,
                  ),
                ),
              ],
            ),
            SizedBox(height: 3.h),
            TextFormField(
              initialValue: '',
              style: TextStyle(fontWeight: FontWeight.bold),
              decoration: InputDecoration(
                filled: true,
                fillColor: mainColor,
                contentPadding:
                    EdgeInsets.symmetric(vertical: 2.h, horizontal: 3.w),
                hintStyle: TextStyle(color: Colors.grey, fontSize: 12.sp),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide(color: Colors.grey.withOpacity(0.3)),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide(color: Colors.grey.withOpacity(0.3)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide(color: thirdColor, width: 2),
                ),
              ),
              inputFormatters: [
                LengthLimitingTextInputFormatter(32),
                FilteringTextInputFormatter.allow(
                    RegExp("[a-zA-Z\u0900-\u097F]")),
              ],
              enabled: true,
              validator: (value1) {
                if (value1!.isEmpty) {
                  return LocaleKeys.Common_please_enter_a_value.tr();
                }
                //
                final RegExp regExp = RegExp(r'^[a-zA-Z\u0900-\u097F]+$');
                if (!regExp.hasMatch(value1)) {
                  return LocaleKeys.Common_please_enter_only_alphabets.tr();
                }
                //
                if (value1.length > 32) {
                  return '${LocaleKeys.Common_maximum_allowed_length_is.tr()} 32';
                }
                return null;
              },
              onSaved: (value1) {
                _newUserInfo['last_name'] = value1!;
              },
            ),
            SizedBox(height: 2.h),
            Text(
              LocaleKeys.FirstTimeScreen_as_mentioned_in_your_identity_card
                  .tr(),
              style: TextStyle(
                fontSize: 10.sp,
                color: Colors.grey,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserPrefsSelection() {
    return Card(
      color: Colors.transparent,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
      ),
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.wc, color: mainContrastColor, size: 20.sp),
                SizedBox(width: 3.w),
                Text(
                  "Interested in",
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: mainContrastColor,
                    fontSize: 14.sp,
                  ),
                ),
              ],
            ),
            SizedBox(height: 3.h),
            Column(
              children: [
                // Woman option
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _newUserInfo['user_prefs_sex'] = 0;
                    });
                  },
                  child: Container(
                    width: double.infinity,
                    margin: EdgeInsets.only(bottom: 1.h),
                    padding: EdgeInsets.symmetric(vertical: 1.5.h),
                    decoration: BoxDecoration(
                      color: _newUserInfo['user_prefs_sex'] == 0
                          ? mainContrastColor
                          : mainColor,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      "Women",
                      style: TextStyle(
                        fontSize: 11.sp,
                        fontWeight: FontWeight.bold,
                        color: _newUserInfo['user_prefs_sex'] == 0
                            ? mainColor
                            : Colors.grey.shade700,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                // Man option
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _newUserInfo['user_prefs_sex'] = 1;
                    });
                  },
                  child: Container(
                    width: double.infinity,
                    margin: EdgeInsets.only(bottom: 1.h),
                    padding: EdgeInsets.symmetric(vertical: 1.5.h),
                    decoration: BoxDecoration(
                      color: _newUserInfo['user_prefs_sex'] == 1
                          ? mainContrastColor
                          : mainColor,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      "Men",
                      style: TextStyle(
                        fontSize: 11.sp,
                        fontWeight: FontWeight.bold,
                        color: _newUserInfo['user_prefs_sex'] == 1
                            ? mainColor
                            : Colors.grey.shade700,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                // Non-binary option
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _newUserInfo['user_prefs_sex'] = 2;
                    });
                  },
                  child: Container(
                    width: double.infinity,
                    margin: EdgeInsets.only(bottom: 1.h),
                    padding: EdgeInsets.symmetric(vertical: 1.5.h),
                    decoration: BoxDecoration(
                      color: _newUserInfo['user_prefs_sex'] == 2
                          ? mainContrastColor
                          : mainColor,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      "Non-binary",
                      style: TextStyle(
                        fontSize: 11.sp,
                        fontWeight: FontWeight.bold,
                        color: _newUserInfo['user_prefs_sex'] == 2
                            ? mainColor
                            : Colors.grey.shade700,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                // Everyone option
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _newUserInfo['user_prefs_sex'] = 3;
                    });
                  },
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsets.symmetric(vertical: 1.5.h),
                    decoration: BoxDecoration(
                      color: _newUserInfo['user_prefs_sex'] == 3
                          ? mainContrastColor
                          : mainColor,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      "Everyone",
                      style: TextStyle(
                        fontSize: 11.sp,
                        fontWeight: FontWeight.bold,
                        color: _newUserInfo['user_prefs_sex'] == 3
                            ? mainColor
                            : Colors.grey.shade700,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 2.h),
            Text(
              "*It cannot be changed later",
              style: TextStyle(
                fontSize: 10.sp,
                color: Colors.grey,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGenderSelection() {
    return Card(
      color: Colors.transparent,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
      ),
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.wc, color: mainContrastColor, size: 20.sp),
                SizedBox(width: 3.w),
                Text(
                  LocaleKeys.FirstTimeScreen_sex.tr(),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: mainContrastColor,
                    fontSize: 14.sp,
                  ),
                ),
              ],
            ),
            SizedBox(height: 3.h),
            Column(
              children: [
                for (int i = 0; i < 3; i++)
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: 1.h),
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _newUserInfo['sex'] = i;
                          // Set preference based on selected sex
                          if (i == 0) {
                            // print('tap : 1');
                            _newUserInfo['user_prefs_sex'] =
                                1; // Woman -> prefers Man
                          } else if (i == 1) {
                            _newUserInfo['user_prefs_sex'] =
                                0; // Man -> prefers Woman
                          } else {
                            _newUserInfo['user_prefs_sex'] =
                                2; // Non-binary -> prefers Non-binary
                          }
                          // print(
                          //     'interest in : ${_newUserInfo['user_prefs_sex']}');
                        });
                      },
                      child: Container(
                        width: double.infinity,
                        padding: EdgeInsets.symmetric(
                            vertical: 1.5.h, horizontal: 4.w),
                        decoration: BoxDecoration(
                          color: _newUserInfo['sex'] == i
                              ? mainContrastColor
                              : mainColor,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          ["Woman", "Man", "Non-binary"][i],
                          style: TextStyle(
                            fontSize: 11.sp,
                            fontWeight: FontWeight.bold,
                            color: _newUserInfo['sex'] == i
                                ? mainColor
                                : Colors.grey.shade700,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
            SizedBox(height: 2.h),
            Text(
              LocaleKeys.FirstTimeScreen_It_cannot_be_changed_later.tr(),
              style: TextStyle(
                fontSize: 10.sp,
                color: Colors.grey,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateOfBirthField() {
    return Card(
      color: Colors.transparent,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
      ),
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.cake, color: mainContrastColor, size: 20.sp),
                SizedBox(width: 3.w),
                Text(
                  LocaleKeys.FirstTimeScreen_dob.tr(),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: mainContrastColor,
                    fontSize: 14.sp,
                  ),
                ),
              ],
            ),
            SizedBox(height: 3.h),
            TextFormField(
              controller: _dateController,
              readOnly: true,
              style: TextStyle(fontWeight: FontWeight.bold),
              decoration: InputDecoration(
                filled: true,
                fillColor: mainColor,
                contentPadding:
                    EdgeInsets.symmetric(vertical: 2.h, horizontal: 3.w),
                hintStyle: TextStyle(color: Colors.grey, fontSize: 12.sp),
                hintText: "DD-MM-YYYY",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide(color: Colors.grey.withOpacity(0.3)),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide(color: Colors.grey.withOpacity(0.3)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide(color: thirdColor, width: 2),
                ),
                suffixIcon:
                    Icon(Icons.calendar_today, color: mainContrastColor),
              ),
              validator: (value1) {
                if (value1!.isEmpty) {
                  return "Please select your date of birth";
                }
                return null;
              },
              onTap: () async {
                isDateClickedOnce = true;
                final pickedDate = await showDatePicker(
                  context: context,
                  initialDate:
                      DateTime.now().subtract(const Duration(days: 365 * 21)),
                  firstDate:
                      DateTime.now().subtract(const Duration(days: 365 * 60)),
                  lastDate:
                      DateTime.now().subtract(const Duration(days: 366 * 18)),
                );
                if (pickedDate != null) {
                  setState(() {
                    _selectedDate = pickedDate;
                    _dateController.text =
                        DateFormat("dd-MM-yyyy").format(pickedDate);
                    _newUserInfo['dob'] = pickedDate.toUtc().toIso8601String();
                  });
                }
              },
            ),
            SizedBox(height: 2.h),
            Text(
              LocaleKeys.FirstTimeScreen_It_cannot_be_changed_later.tr(),
              style: TextStyle(
                fontSize: 10.sp,
                color: Colors.grey,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCitySelection() {
    return Card(
      color: Colors.transparent,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
      ),
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.location_city,
                    color: mainContrastColor, size: 20.sp),
                SizedBox(width: 3.w),
                Text(
                  LocaleKeys.FirstTimeScreen_city.tr(),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: mainContrastColor,
                    fontSize: 14.sp,
                  ),
                ),
              ],
            ),
            SizedBox(height: 3.h),
            TypeAheadField(
              textFieldConfiguration: TextFieldConfiguration(
                controller: _typeAheadController,
                style: TextStyle(fontWeight: FontWeight.bold),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp("[a-zA-Z]")),
                ],
                decoration: InputDecoration(
                  filled: true,
                  fillColor: mainColor,
                  contentPadding:
                      EdgeInsets.symmetric(vertical: 2.h, horizontal: 3.w),
                  hintText: "Search for your city",
                  hintStyle: TextStyle(color: Colors.grey, fontSize: 12.sp),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: BorderSide(color: Colors.grey.withOpacity(0.3)),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: BorderSide(color: Colors.grey.withOpacity(0.3)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: BorderSide(color: thirdColor, width: 2),
                  ),
                  prefixIcon: Icon(Icons.search, color: mainContrastColor),
                ),
              ),
              suggestionsCallback: (pattern) async {
                if (pattern.length < 2) return [];
                return await Provider.of<DatabaseProvider>(context,
                        listen: false)
                    .searchQuery(pattern);
              },
              suggestionsBoxDecoration: SuggestionsBoxDecoration(
                constraints: BoxConstraints(maxHeight: 25.h),
                elevation: 4.0,
                color: mainColor,
                borderRadius: BorderRadius.circular(10),
              ),
              itemBuilder: (context, suggestion) {
                return ListTile(
                  title: Text(
                    '${suggestion['city_name']}, ${suggestion['state_name']}',
                    style: TextStyle(
                      color: mainContrastColor,
                      fontSize: 12.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  subtitle: Text(
                    suggestion['country_name'],
                    style: TextStyle(
                      color: mainContrastColor.withOpacity(0.8),
                      fontSize: 10.sp,
                    ),
                  ),
                );
              },
              onSuggestionSelected: (suggestion) {
                setState(() {
                  _newUserInfo['city_name'] = suggestion['city_name'];
                  _newUserInfo['state_name'] = suggestion['state_name'];
                  _newUserInfo['country_name'] = suggestion['country_name'];
                  _typeAheadController.text = suggestion['city_name'];
                });
              },
              noItemsFoundBuilder: (context) => Padding(
                padding: EdgeInsets.all(2.w),
                child: Text(
                  'No cities found',
                  style: TextStyle(
                    color: mainContrastColor,
                    fontSize: 12.sp,
                  ),
                ),
              ),
              loadingBuilder: (context) => Padding(
                padding: EdgeInsets.all(2.w),
                child: Row(
                  children: [
                    SizedBox(
                      width: 4.w,
                      height: 4.w,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(thirdColor),
                      ),
                    ),
                    SizedBox(width: 3.w),
                    Text(
                      'Searching...',
                      style: TextStyle(
                        color: mainContrastColor,
                        fontSize: 12.sp,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 3.h),
            if (_newUserInfo['city_name'] != '')
              Center(
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: 1.h, horizontal: 3.w),
                  decoration: BoxDecoration(
                    color: thirdColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: thirdColor.withOpacity(0.3)),
                  ),
                  child: Text(
                    '${_newUserInfo['city_name']}, ${_newUserInfo['state_name']}, ${_newUserInfo['country_name']}',
                    style: TextStyle(
                      fontSize: 13.sp,
                      fontWeight: FontWeight.w600,
                      color: mainContrastColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _handleNext() async {
    if (currenTask == 1) {
      myAnalytics.setLogEvent('FirstTimeScreen_FirstName', {});
    }
    if (currenTask == 2) {
      myAnalytics.setLogEvent('FirstTimeScreen_Lastname', {});
    }
    if (currenTask == 3) {
      myAnalytics.setLogEvent('FirstTimeScreen_Sex', {});
    }
    if (currenTask == 4) {
      myAnalytics.setLogEvent('FirstTimeScreen_DateOfBirth', {});
    }
    if (currenTask == 5) {
      myAnalytics.setLogEvent('FirstTimeScreen_City', {});
    }

    if (currenTask == 4 && isDateClickedOnce == false) {
      callToast(LocaleKeys.FirstTimeScreen_Please_select_a_date.tr());
      return;
    }
    if (currenTask == 5 && _newUserInfo['city_name'] == "") {
      callToast(LocaleKeys.FirstTimeScreen_Please_select_a_city.tr());
      return;
    }

    if (!_referralKey.currentState!.validate()) {
      return;
    }
    _referralKey.currentState!.save();

    if (currenTask <= totalTask) {
      _referralKey.currentState!.reset();
      setState(() {
        currenTask++;
      });
      if (currenTask > totalTask) {
        setState(() {
          _isLoading = true;
        });
        await Provider.of<AuthProvider>(context, listen: false)
            .updateUserInfoFirstTime(_newUserInfo);

        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
