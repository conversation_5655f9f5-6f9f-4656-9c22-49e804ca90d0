// import 'dart:developer' as developer;

// class StartupTimer {
//   static DateTime? _appStartTime;
//   static DateTime? _firstFrameTime;
  
//   static void markAppStart() {
//     _appStartTime = DateTime.now();
//     developer.log('🚀 App startup initiated', name: 'StartupTimer');
//   }
  
//   static void markFirstFrame() {
//     _firstFrameTime = DateTime.now();
//     if (_appStartTime != null) {
//       final duration = _firstFrameTime!.difference(_appStartTime!);
//       developer.log('✅ First frame rendered in ${duration.inMilliseconds}ms', name: 'StartupTimer');
//     }
//   }
  
//   static void logOperation(String operation, int milliseconds) {
//     developer.log('⏱️ $operation: ${milliseconds}ms', name: 'StartupTimer');
//   }
  
//   static int? getTotalStartupTime() {
//     if (_appStartTime != null && _firstFrameTime != null) {
//       return _firstFrameTime!.difference(_appStartTime!).inMilliseconds;
//     }
//     return null;
//   }
// }
