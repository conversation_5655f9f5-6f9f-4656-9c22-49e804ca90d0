import 'dart:developer' as developer;

class PerformanceMonitor {
  static final Map<String, DateTime> _startTimes = {};
  static final Map<String, Duration> _durations = {};
  
  static void startTimer(String operation) {
    _startTimes[operation] = DateTime.now();
    developer.log('⏱️ START: $operation', name: 'Performance');
  }
  
  static void endTimer(String operation) {
    final startTime = _startTimes[operation];
    if (startTime != null) {
      final duration = DateTime.now().difference(startTime);
      _durations[operation] = duration;
      developer.log('✅ END: $operation - ${duration.inMilliseconds}ms', name: 'Performance');
      _startTimes.remove(operation);
    }
  }
  
  static void logAppStartupComplete() {
    final totalTime = _durations.values.fold(Duration.zero, (sum, duration) => sum + duration);
    developer.log('🚀 APP STARTUP COMPLETE - Total: ${totalTime.inMilliseconds}ms', name: 'Performance');
    
    // Log breakdown
    _durations.forEach((operation, duration) {
      developer.log('  - $operation: ${duration.inMilliseconds}ms', name: 'Performance');
    });
  }
  
  static Duration? getDuration(String operation) {
    return _durations[operation];
  }
  
  static void clear() {
    _startTimes.clear();
    _durations.clear();
  }
}
