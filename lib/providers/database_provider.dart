import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:dio/dio.dart';
import 'package:eventsource/eventsource.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_client_sse/flutter_client_sse.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:geolocator/geolocator.dart';
import 'package:zupid/models/myPersonalityTraits.dart';
import 'package:zupid/models/myReferral.dart';
import 'package:http/http.dart' as http;
import 'package:zupid/models/myPromptList.dart';
import 'package:zupid/models/myUserPrefs.dart';
import 'package:path_provider/path_provider.dart';

import '../common_things/my_constants.dart';
import '../common_things/my_fucntions.dart';
import '../models/myChat.dart';
import '../models/myInterest.dart';
import '../models/myChatDetails.dart';
import '../models/myUserInfo.dart';
import '../models/myUserInfo2.dart';

class DatabaseProvider with ChangeNotifier {
  DatabaseProvider() {
    _init();
  }
  _init() {
    mySecureStorage = FlutterSecureStorage();
  }

  //
  static const int fetchProfileCutoffNumber = 5;
  int _nextProfileImageToLoad = 2; // Start at 3 since 0-2 are loaded initially
  final Dio _dio = Dio();
  //
  String _tempDirectoryForImage = "-1";
  FlutterSecureStorage? mySecureStorage;
  String? _token;
  String? _userId;
  // user account details
  bool isBlocked = false;
  int _phoneNumber = 0;
  MyUserInfo? _myUserInfo;
  MyUserInfo2? _myUserInfo2;
  MyUserPrefs? _myUserPrefs;
  MyReferral? _myReferral;
  int _verifyStatus = 0;
  bool _isPersonalInfoDone = false;
  //
  Map<int, int> _userInfo2Params = {1: 0, 2: 0, 3: 0, 4: 0};
  //
  List<MyPrompt> _promptList = [];
  List<MyInterest> _allInterestList = [];
  List<MyPersonalityTraits> _allPersonalityTraitsList = [];
  Map<String, List<String>> _userSignedUrlMap = {};
  Map<String, List<String>> _userPreSignedUrlMap = {};
  //
  int _swipeScreenOffset = 0;
  List<MyUserInfo> _swipeScreenProfilesList = [];
  int _currentSwipeScreenProfileIndex = 0;
  bool _noSwipeProfilesLeft = false;
  //
  int _whoLikedYouScreenOffset = 0;
  List<MyUserInfo> _whoLikedYouProfilesList = [];
  bool _noWhoLikedYouProfilesLeft = false;
  Map<String, String> _whoLikedYouMessageMap = {};
  //
  //
  List<MyUserInfo> _similarProfilesList = [];
  List<String> _similarProfilesUserIdList = [];
  //
  List<MyChatDetails> _chatDetailsList = [];
  Map<String, MyUserInfo> _userIdUserInfoMap = {};
  //
  Map<String, List<MyChat>> _userIdChatListMap = {};
  Map<String, int> _userIdChatOffsetMap = {};
  //
  Map<String, MyUserInfo> _userIdMatchedUserInfoMap = {};
  ////
  ////////
  Map<String, bool> _dataLoadedMap = {
    'loadall': false,
    'myUserInfo': false,
    'myUserInfo2': false,
    'myUserPrefs': false,
    'allPromptList': false,
    'allInterests': false,
    'swipeScreen': false,
    'whoLikedYouScreen': false,
    'similarScreen': false,
    'matchedProfiles': false,
    'verifyProfile': false,
  };
  //
  MyReferral get getMyReferral {
    return _myReferral!;
  }

  Map<String, bool> get getDataLoadedMap {
    return _dataLoadedMap;
  }

  ////
  String get tempDirectoryForImage {
    return _tempDirectoryForImage;
  }

  List<MyPersonalityTraits> get getAllPersonalityTraitsList {
    return _allPersonalityTraitsList;
  }

  ///
  bool get isTwoPhotos {
    return (_userSignedUrlMap[_myUserInfo!.userId]![0] != "-1") &&
        (_userSignedUrlMap[_myUserInfo!.userId]![1] != "-1");
  }

  bool get areAllPhotosPresent {
    return _userSignedUrlMap[_myUserInfo!.userId]!.every((url) => url != "-1");
  }

  bool get isMovieCheck {
    // print('movie check: ${_myUserInfo!.allInterests.movies.length}');
    return _myUserInfo!.allInterests.movies.length >= 3;
  }

  //
  int get getMyPhoneNumber {
    return _phoneNumber;
  }

  //
  int get getVerifyProfile {
    return _verifyStatus;
  }

  MyUserInfo get getMyUserInfo {
    return _myUserInfo!;
  }

  MyUserInfo2 get getMyUserInfo2 {
    return _myUserInfo2!;
  }

  Map<int, int> get getUserInfo2Params {
    return _userInfo2Params;
  }

  List<MyPrompt> get getPromptList {
    return _promptList;
  }

  List<MyInterest> get getInterestList {
    return _allInterestList;
  }

  MyUserPrefs get getMyUserPrefs {
    return _myUserPrefs!;
  }

  //
  Map<String, List<String>> get getUserSignedUrlMap {
    return _userSignedUrlMap;
  }

  // swipe gets //
  List<MyUserInfo> get getSwipeScreenProfilesList {
    return _swipeScreenProfilesList;
  }

  int get getCurrentSwipeScreenProfileIndex {
    return _currentSwipeScreenProfileIndex;
  }

  bool get getNoSwipeProfilesLeft {
    return _noSwipeProfilesLeft;
  }

  //
  // swipe gets //
  List<MyUserInfo> get getWhoLikedYouProfilesList {
    return _whoLikedYouProfilesList;
  }

  bool get getNoWhoLikedYouProfilesLeft {
    return _noWhoLikedYouProfilesLeft;
  }

  Map<String, String> get getWhoLikedYouMessageMap {
    return _whoLikedYouMessageMap;
  }

  //
  List<MyUserInfo> get getSimilarProfilesList {
    return _similarProfilesList;
  }

  List<MyChatDetails> get getMatchedChatDetailsList {
    return _chatDetailsList;
  }

  /////
  Map<String, MyUserInfo> get getUserIdMatchedUserInfoMap {
    return _userIdMatchedUserInfoMap;
  }

  bool get isPersonalInfoDone => _isPersonalInfoDone;

  void changeIsPersonalInfoDoneTrue() {
    // print('Chnaging personal info done to: $_isPersonalInfoDone');
    _isPersonalInfoDone = true;
    notifyListeners();
  }

  //// reset all /// mainly used for logout //
  void resetAll() {
    mySecureStorage = null;
    _token = null;
    _userId = null;
    // user account details
    isBlocked = false;
    _phoneNumber = 0;
    // not making userinfo as null as it is causing red screen on logout screen
    // _myUserInfo = null;
    _myUserInfo2 = null;
    _myUserPrefs = null;
    _verifyStatus = 0;
    _userInfo2Params = {1: 0, 2: 0, 3: 0, 4: 0};
    //
    _promptList = [];
    _allInterestList = [];
    _userSignedUrlMap = {};
    //
    _swipeScreenOffset = 0;
    _swipeScreenProfilesList = [];
    _currentSwipeScreenProfileIndex = 0;
    _noSwipeProfilesLeft = false;
    //
    _whoLikedYouScreenOffset = 0;
    _whoLikedYouProfilesList = [];
    _noWhoLikedYouProfilesLeft = false;
    //
    _similarProfilesList = [];
    _similarProfilesUserIdList = [];
    //
    _chatDetailsList = [];
    _userIdUserInfoMap = {};
    //
    _userIdChatListMap = {};
    _userIdChatOffsetMap = {};
    //
    _userIdMatchedUserInfoMap = {};
    ////
  }

  void addIndexByOne() {
    _currentSwipeScreenProfileIndex += 1;

    if (_swipeScreenProfilesList.length - _currentSwipeScreenProfileIndex <=
            fetchProfileCutoffNumber &&
        _noSwipeProfilesLeft == false) {
      fetchSwipeScreenProfiles(false);
    }
    notifyListeners();
  }

  void callNotifyListner() {
    notifyListeners();
  }

  // void checkIfSwipeUserIdInSimilarProfilesList() {
  //   // print('checking is swipe screen has any similar profiles');
  //   while (_similarProfilesUserIdList.contains(
  //       _swipeScreenProfilesList[_currentSwipeScreenProfileIndex].userId)) {
  //     _currentSwipeScreenProfileIndex += 1;
  //     if (_currentSwipeScreenProfileIndex >= _swipeScreenProfilesList.length) {
  //       break;
  //     }
  //     // print(
  //     //     'skipping userId : ${_swipeScreenProfilesList[_currentSwipeScreenProfileIndex].userId}');
  //   }

  //   if (_swipeScreenProfilesList.length - _currentSwipeScreenProfileIndex <= fetchProfileCutoffNumber &&
  //       _noSwipeProfilesLeft == false) {
  //     fetchSwipeScreenProfiles();
  //   }
  //   // zupid
  //   // notifyListeners();
  // }

  void reduceUserInfo2ParamsByOne(int type) {
    // print(_userInfo2Params);
    // print('112');
    // print('Type : $type');
    // is user has membership then don't reduce like
    if (type != -1) {
      // if (type == 1 && _myUserInfo!.isMembership) {
      //   // do nothing
      // } else {
      _userInfo2Params[type] = _userInfo2Params[type]! - 1;
      // }
      notifyListeners();
      // i have removed if as tehre is limit of 20 not unlimited like in heydosti
      // for now only send message no super like and all
    }
  }

  void updateUserInfo2Params(MyUserInfo2 _myUserInfo2) {
    _userInfo2Params[1] = _myUserInfo2.likesLeft;
    _userInfo2Params[2] = _myUserInfo2.superLikesLeft;
    _userInfo2Params[3] = _myUserInfo2.messageLeft;
    _userInfo2Params[4] = _myUserInfo2.boostLeft;
  }

  ////
  void resetSwipeProfilesList() {
    _dataLoadedMap['swipeScreen'] = false;
    _currentSwipeScreenProfileIndex = 0;
    _noSwipeProfilesLeft = false;
    _swipeScreenOffset = 0;
    _swipeScreenProfilesList = [];
  }

  // ///////////// Download Images using Flutter Cache Manager ///////////
  // Future<void> downloadImagesFunc(String url) async {
  //   // print('Downloading Image Url : $url');
  //   // var randomFile = await DefaultCacheManager().getSingleFile(url);
  // }

  Future<String> fetchAuthTokenAndUserId() async {
    // print('START: fetching userid and token in db provider');
    String? x = await mySecureStorage!.read(key: 'token');
    // print(x);
    if (x != null) {
      _token = x;
    }
    String? y = await mySecureStorage!.read(key: 'userId');
    if (y != null) {
      _userId = y;
    }
    // print(y);
    // print('END: fetching userid and token in db provider');
    return _userId!;
  }

  ///// Now load userInfo Data from userinfo path
  Future<bool> fetchMyUserAccount() async {
    // Read value
    // print('fetching useraccount...');
    try {
      Map jsonBody = {"user_id": _userId};
      http.Response response = await http.post(routeUrl(getUserAccountRoute),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $_token',
          },
          body: jsonEncode(jsonBody));
      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        isBlocked = jsonData['blocked'];
        _phoneNumber = jsonData['phone_number'];
      }
    } catch (err) {
      print(err);
    }
    return isBlocked;
  }

  /// Now load userInfo Data from userinfo path
  Future<bool> fetchMyUserInfo() async {
    // Read value
    // print('fetching userinfo for userid : $_userId');
    Map jsonBody = {"user_id": _userId, "var_user_id": _userId};
    http.Response response = await http.post(routeUrl(getUserInfoRoute),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode(jsonBody));
    if (response.statusCode == 200) {
      final jsonData = json.decode(response.body);
      _myUserInfo = MyUserInfo.fromMap(jsonData);
      fetchAllImages(_myUserInfo!);
      //
      _dataLoadedMap['myUserInfo'] = true;
      //
      return true;
    }
    return false;
  }

  ////
  Future<void> updateUserInfo(String _routeUrl, Map jsonBody) async {
    // print('updating user info: $_routeUrl');
    jsonBody['user_id'] = _userId;
    // print(jsonBody);
    http.Response response = await http.post(routeUrl(_routeUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode(jsonBody));
    final jsonData = json.decode(response.body);
    // print(jsonData);
    if (response.statusCode == 200) {
      _myUserInfo = MyUserInfo.fromMap(jsonData);
      notifyListeners();
    } else {
      // print(response.statusCode);
    }
  }

  //
  Future<void> updateUserInfoLastSeen() async {
    // print('updating user info last seen:');
    Map<String, String> jsonBody = {'user_id': _myUserInfo!.userId};
    http.Response response = await http.post(routeUrl(userInfoLastSeenRoute),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode(jsonBody));
    final jsonData = json.decode(response.body);
  }

  // get all prompt list
  Future<void> fetchAllPromptList() async {
    // print('Fetching All Prompt List...');
    Map jsonBody = {};
    //
    jsonBody['user_id'] = _userId;
    http.Response response = await http.post(routeUrl(promptListRoute),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode(jsonBody));
    final jsonData = json.decode(response.body);
    // print(jsonData);
    if (response.statusCode == 200) {
      if (jsonData != null) {
        for (Map<String, dynamic> item in jsonData) {
          _promptList.add(MyPrompt.fromMap(item));
        }
      }
      //
      _dataLoadedMap['allPromptList'] = true;
      //

      notifyListeners();
    }
  }

  ///// get all prompt list
  Future<void> fetchMyUserPrefs() async {
    // print('Fetching User prefs....');
    Map jsonBody = {};
    //
    jsonBody['user_id'] = _userId;
    try {
      http.Response response = await http.post(
        routeUrl(getUserPrefsRoute),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode(jsonBody),
      );
      // print(response);
      final jsonData = json.decode(response.body);
      // print(jsonData);
      if (response.statusCode == 200) {
        _myUserPrefs = MyUserPrefs.fromMap(jsonData);
        _dataLoadedMap['myUserPrefs'] = true;
        notifyListeners();
      }
    } catch (error) {
      // print(error);
    }
  }

  //// update user prefs
  Future<void> updateUserPrefs(String _routeUrl, Map jsonBody) async {
    // print('updating user prefs');
    jsonBody['user_id'] = _userId;
    // print('1');

    http.Response response = await http.post(
      routeUrl(_routeUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $_token',
      },
      body: jsonEncode(jsonBody),
    );
    // print('2');
    final jsonData = json.decode(response.body);
    // print('3');

    // print(jsonData);
    if (response.statusCode == 200) {
      //print('4');
      _myUserInfo = MyUserInfo.fromMap(jsonData['user_info']);
      _myUserPrefs = MyUserPrefs.fromMap(jsonData['user_prefs']);
      // to reset all the swipe profiles list //
      resetSwipeProfilesList();
      fetchSwipeScreenProfiles(true);
      notifyListeners();
    }
  }

  // upload image to server
  Future<void> uploadImage(File imageFile, int i) async {
    File uploadedFile = imageFile;
    // print('uploading image...');
    // jsonBody['user_id'] = _userId;
    //
    final request = http.MultipartRequest('POST', routeUrl(uploadImageRoute))
      ..headers.addAll({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $_token',
      })
      ..fields['user_id'] = _userId!
      ..fields['phone_number'] = _phoneNumber.toString()
      ..fields['photo_i'] = i.toString()
      ..files.add(http.MultipartFile.fromBytes(
        'image',
        imageFile.readAsBytesSync(),
        filename: 'upload.webp',
      ));
    //
    final response = await request.send();
    final responseString = await response.stream.bytesToString();

    // Parse the response JSON and get the signed URL

    if (response.statusCode == 200) {
      final directory = await getTemporaryDirectory();
      _tempDirectoryForImage = '${directory.path}/images/';

      // Append a unique identifier (timestamp) to the filename to avoid caching issues
      final timestamp =
          DateTime.now().millisecondsSinceEpoch; // Unique identifier
      final fileName = '${_phoneNumber.toString()}_${i}_$timestamp.webp';
      final filePath = '$_tempDirectoryForImage$fileName';
      // print(filePath);
      // Update your map or state as needed
      _userSignedUrlMap[_myUserInfo!.userId]![i] = fileName;

      // Write the file to the new path
      await imageFile.copy(filePath);

      notifyListeners();

      // return signedUrl;
    } else {
      // print(response.request);
      // print('Error uploading image..!!');
      notifyListeners();
    }
  }

  ////
  Future<void> fetchAllImages(MyUserInfo varMyUserInfo) async {
    if (_userSignedUrlMap.containsKey(varMyUserInfo.userId)) {
      // If the variable already exists, check each item
      // this condiot is added specficly to avoid  relaod of chatdetails photo
      for (int i = 0; i < 6; i++) {
        if (_userSignedUrlMap[varMyUserInfo.userId]![i] == '-1') {
          _userSignedUrlMap[varMyUserInfo.userId]![i] = "isLoading";
        }
      }
    } else {
      // If the variable does not exist, initialize it with "isLoading" values
      _userSignedUrlMap[varMyUserInfo.userId] = [
        "isLoading",
        "isLoading",
        "isLoading",
        "isLoading",
        "isLoading",
        "isLoading"
      ];
    }
    for (int i = 0; i < 6; i = i + 1) {
      String urlValue = varMyUserInfo.photosName[i];
      if (urlValue == "-1") {
        _userSignedUrlMap[varMyUserInfo.userId]![i] = "-1";
        notifyListeners();
      } else {
        fetchSignedFromUrl(urlValue, varMyUserInfo.userId, i)
            .then((value) async {
          _userSignedUrlMap[varMyUserInfo.userId]![i] = value;
          notifyListeners();
        });
      }
    }
  }

  //////
  Future<String> fetchSignedFromUrl(
      String imageUrl, String userId, int i) async {
    // print('fetching signed url for $userId');
    if (_userPreSignedUrlMap.containsKey(userId)) {
      // print('fetching signed url from pre signed url map for $userId');

      await downloadTempImage(_userPreSignedUrlMap[userId]![i], imageUrl);
      return imageUrl;
    }
    // print('Fetching Signed Image...');
    Map jsonBody = {"image_url": imageUrl, "user_id": _userId};
    // jsonBody['user_id'] = _userId;

    http.Response response = await http.post(routeUrl(fetchImageRoute),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode(jsonBody));
    final jsonData = json.decode(response.body);
    // print(jsonData);

    if (response.statusCode == 200) {
      // download image using cache manager //
      // downloadImagesFunc(jsonData['image_url'].toString());
      await downloadTempImage(jsonData['image_url'].toString(), imageUrl);
      //
      // return jsonData['image_url'].toString();
      return imageUrl;
      // notifyListeners();
    } else {
      return "-1";
    }
  }

  // get all interest
  Future<void> fetchAllInterests() async {
    // print('Fetching All Interests....');
    Map jsonBody = {};
    //
    jsonBody['user_id'] = _userId;
    http.Response response = await http.post(routeUrl(allInterestsRoute),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode(jsonBody));
    final jsonData = json.decode(response.body);
    // print(jsonData);
    if (response.statusCode == 200) {
      if (jsonData != null) {
        for (Map<String, dynamic> item in jsonData) {
          _allInterestList.add(MyInterest.fromMap(item));
        }
      }
      _dataLoadedMap['allInterests'] = true;
    }
  }

  ////
  // get all interest
  Future<void> EditInterests(List<String> userInterests) async {
    // print('Update Edit Interests...');
    // update local first // it is used in home screen toq uickly go
    //throigh next iteration of selct category //
    // Update AllInterests with the new interests list
    AllInterests updatedAllInterests =
        _myUserInfo!.allInterests.copyWith(interests: userInterests);
    _myUserInfo = _myUserInfo!.copyWith(allInterests: updatedAllInterests);
    notifyListeners();
    //
    Map jsonBody = {
      "user_id": _userId,
      "interest": userInterests,
    };

    //
    http.Response response = await http.post(routeUrl(editInterestsRoute),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode(jsonBody));
    final jsonData = json.decode(response.body);
    // print(jsonData);
    if (response.statusCode == 200) {
      _myUserInfo = MyUserInfo.fromMap(jsonData);
      // notifyListeners();
    }
  }

  //// donwload swipe screen profiles///
  // get all interest
  Future<void> fetchSwipeScreenProfiles(bool isPrefs) async {
    // print('Fetching All Swipe Screens profiles....');
    Map jsonBody = {
      "user_id": _userId,
      "offset": isPrefs ? 0 : fetchProfileCutoffNumber + 1,

      // IMP: used fetchprofilecutoffnumber ex:5, as I have added remove matching
      // placed already. so when it pull data after 5 swipes then next 5 will again
      // show up from 0. so when i pull it using offset 10 then it will basically
      // be offset 10 (-5) as intial 5 will also show up. Now after this change
      // only ignore first 5 next will come as matching will be there already
    };
    //
    http.Response response = await http.post(routeUrl(swipeScreenProfilesRoute),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode(jsonBody));
    final jsonData = json.decode(response.body);
    int total = jsonData['total'] ?? 0;
    // print('Total Profiles fetched: $total');
    if (response.statusCode == 200) {
      if (total > 0) {
        for (int i = 0; i < total; i = i + 1) {
          MyUserInfo _userInfo = MyUserInfo.fromMap(jsonData['data'][i]);
          _swipeScreenProfilesList.add(_userInfo);
          // now call fetch images fucntion
          fetchAllImages(_userInfo);
        }
      }
      if (_dataLoadedMap['swipeScreen'] == false) {
        _dataLoadedMap['swipeScreen'] = true;
        // print('updated swipes screen laoded to true');
      }
    }
    _swipeScreenOffset += total;
    if (total == 0) {
      _noSwipeProfilesLeft = true;
      // print('--- No Profiles Left ---');
      notifyListeners();
    } else {}
  }

  ///

  // get all interest
  Future<MyChatDetails?> addOrUpdateMatching(
      MyUserInfo varUserInfo, int status, String message) async {
    // now call reduce userinfo2 params//
    reduceUserInfo2ParamsByOne(status);
    //
    // print('Add new matching row or update existing...');
    Map jsonBody = {
      "user_id": _userId,
      "user_id_2": varUserInfo.userId,
      "status": status,
      "message": message
    };
    //
    // print(jsonBody);
    http.Response response = await http.post(routeUrl(addOrUpdateMatchingRoute),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode(jsonBody));
    final jsonData = json.decode(response.body);
    // print(jsonData);
    if (response.statusCode == 200) {
      // now fetch data of matching and chatdetails and store into db
      var myChatDetailsData = jsonData['chat_details'];
      if (myChatDetailsData != null) {
        MyChatDetails varMyChatDetails = MyChatDetails(
          id: myChatDetailsData['id'],
          userId1: myChatDetailsData['user_id_1'],
          userId2: myChatDetailsData['user_id_2'],
          unread1: myChatDetailsData['unread_1'],
          unread2: myChatDetailsData['unread_2'],
          mute1: myChatDetailsData['mute_1'],
          mute2: myChatDetailsData['mute_2'],
          lastMessage: myChatDetailsData['last_message'],
          lastMessageTime:
              DateTime.parse(myChatDetailsData['last_message_time']),
          firstName: varUserInfo.firstName,
          photosName: varUserInfo.photosName,
          updatedAt: DateTime.parse(myChatDetailsData['updated_at']),
          createdAt: DateTime.parse(myChatDetailsData['created_at']),
        );

        // now add it to list of mychatdetails list
        _chatDetailsList.insert(0, varMyChatDetails);
        _userIdChatListMap[varUserInfo.userId] = [];
        _userIdChatOffsetMap[varUserInfo.userId] = 0;
        // print('udpated match');
        // now fetch chats //
        fetchChatsForUserId(varUserInfo.userId, true);
        //
        print('Matching added or updated!');
        notifyListeners();
        return varMyChatDetails;
        //
      }
    } else {
      print('MatchingUpdate : some error occured..!!');
    }
    return null;
  }

  /////////////////
  //// donwload who liked you profiles///
  Future<void> fetchWhoLikedYou() async {
    print('Fetching Who Liked you .....');
    Map jsonBody = {
      "user_id": _userId,
      "offset": _whoLikedYouScreenOffset,
    };
    //
    http.Response response =
        await http.post(routeUrl(whoLikedYouScreenProfilesRoute),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $_token',
            },
            body: jsonEncode(jsonBody));
    final jsonData = json.decode(response.body);
    int total = jsonData['total'] ?? 0;
    print('Total Who Liked you Profiles: $total');
    if (response.statusCode == 200) {
      if (total > 0) {
        for (int i = 0; i < total; i = i + 1) {
          MyUserInfo _userInfo = MyUserInfo.fromMap(jsonData['data'][i]);
          _whoLikedYouProfilesList.add(_userInfo);
          // now call fetch images fucntion
          fetchAllImages(_userInfo);
        }
      }
      if (_dataLoadedMap['whoLikedYouScreen'] == false) {
        _dataLoadedMap['whoLikedYouScreen'] = true;
      }
    }
    _whoLikedYouScreenOffset += total;
    if (total == 0) {
      _noWhoLikedYouProfilesLeft = true;
      print('--- No Who liked you Profiles Left ---');
      notifyListeners();
    } else {}
  }

  void removeProfileFromAnyList(int index, String type) {
    // print('remove profile at index $index from $type');
    //
    if (type == 'liked') {
      _whoLikedYouProfilesList.removeAt(index);
    } else if (type == 'similar') {
      _similarProfilesList.removeAt(index);
    }
    notifyListeners();
  }

  ///
  /////////////////////////
  Future<void> fetchSimilarProfile() async {
    print('Fetching Similar Profiles...');
    Map jsonBody = {
      "user_id": _userId,
    };
    //
    http.Response response = await http.post(routeUrl(similarProfilesRoute),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode(jsonBody));
    final jsonData = json.decode(response.body);
    if (response.statusCode == 200) {
      List<String> user_id_list = [];
      if (jsonData['profile_list'] != null) {
        user_id_list = List<String>.from(jsonData["profile_list"]);
        for (String var_user_id in user_id_list) {
          MyUserInfo? varUserInfo =
              await fetchVarUserInfoForSimilarProfile(var_user_id);
          //
          _similarProfilesList.add(varUserInfo);
          // print(varUserInfo.userId);
          _similarProfilesUserIdList.add(varUserInfo.userId);
        }
        //
      }
      if (_dataLoadedMap['similarScreen'] == false) {
        _dataLoadedMap['similarScreen'] = true;
      }
      print('Similar Screen fetched: DONE!');
    }
  }

  //
  Future<void> updateSimilarProfileList(String userId) async {
    print('Update Similar Profiles list...');
    //
    List<String> _newList = [];
    //
    _similarProfilesList.forEach((element) {
      if (element.userId != userId) {
        _newList.add(element.userId);
      }
      print('New Similar Profile List : $_newList');
    });

    Map jsonBody = {"user_id": _userId, "profile_list": _newList};
    //
    http.Response response =
        await http.post(routeUrl(similarProfilesUpdateRoute),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $_token',
            },
            body: jsonEncode(jsonBody));
    // final jsonData = json.decode(response.body);
    //
    if (response.statusCode == 200) {
      print('updates similar profile list!');
    }
  }

  ///
  Future<MyUserInfo> fetchVarUserInfoForSimilarProfile(String varUserId) async {
    MyUserInfo? _varMyUserInfo;
    // Read value
    // print('fetching userinfo for varUserId : $varUserId');
    Map jsonBody = {"user_id": _userId, "var_user_id": varUserId};
    http.Response response = await http.post(routeUrl(getUserInfoRoute),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode(jsonBody));
    if (response.statusCode == 200) {
      final jsonData = json.decode(response.body);
      // print(jsonData);
      _varMyUserInfo = MyUserInfo.fromMap(jsonData);
      _userIdMatchedUserInfoMap[varUserId] = _varMyUserInfo;
      //////
      fetchAllImages(_varMyUserInfo);
    }
    return _varMyUserInfo!;
  }

  //// fetch all matched profiles (not blocked by you or him)////
  ////////////////////////////
  // Future<void> fetchAllMatchedProfiles() async {
  //   print('Fetching Matched Profiles...');
  //   Map jsonBody = {
  //     "user_id": _userId,
  //   };
  //   //
  //   http.Response response = await http.post(routeUrl(chatDetailRoute),
  //       headers: {
  //         'Content-Type': 'application/json',
  //         'Authorization': 'Bearer $_token',
  //       },
  //       body: jsonEncode(jsonBody));
  //   final jsonData = json.decode(response.body);
  //   int total = jsonData['total'] ?? 0;
  //   print('Total Matched Profiles: $total');
  //   if (response.statusCode == 200) {
  //     if (total > 0) {
  //       for (int i = 0; i < total; i = i + 1) {
  //         MyChatDetails _matchedChatDetails =
  //             MyChatDetails.fromMap(jsonData['data'][i]);
  //         _chatDetailsList.add(_matchedChatDetails);
  //         // now call fetch images fucntion
  //         // fetchAllImages(_userInfo);
  //         String userId = findVarUserIdFromChatDetails(
  //             _matchedChatDetails, _myUserInfo!.userId);
  //         fetchAImage(userId, _matchedChatDetails.photosName[0]);
  //         // now make map for this //
  //         _userIdChatListMap[userId] = [];
  //         _userIdChatOffsetMap[userId] = 0;
  //         //
  //       }
  //       if (_dataLoadedMap['matchedProfiles'] == false) {
  //         _dataLoadedMap['matchedProfiles'] = true;
  //       }
  //     }
  //   }
  // }

  // fetch only 1st image of array //
  Future<void> fetchAImage(String varUserId, String urlValue) async {
    // print('Fetching A Image for UserId: $varUserId');
    _userSignedUrlMap[varUserId] = ["isLoading", "-1", "-1", "-1", "-1", "-1"];
    int i = 0;
    if (urlValue == "-1") {
      _userSignedUrlMap[varUserId]![i] = "-1";
      notifyListeners();
    } else {
      fetchSignedFromUrl(urlValue, varUserId, i).then((value) async {
        _userSignedUrlMap[varUserId]![i] = value;
        notifyListeners();
      });
    }
  }

  /////
  Future<void> addAChat(String receiverId, String message, String chatDetailsId,
      int whichUser) async {
    Random random = Random();
    String randomNumber = random.nextInt(1000000).toString();
    _userIdChatListMap[receiverId]!.insert(
      0,
      MyChat(
          id: randomNumber,
          createdAt: DateTime.now().subtract(Duration(hours: 5, minutes: 30)),
          updatedAt: DateTime.now().subtract(Duration(hours: 5, minutes: 30)),
          senderId: _userId!,
          recieverId: receiverId,
          message: message),
    );
    notifyListeners();
    //
    print('Create a Chat...');
    Map jsonBody = {
      "user_id": _userId,
      "reciever_id": receiverId,
      "message": message,
      "chat_details_id": chatDetailsId,
      "which_user": whichUser,
    };
    //
    http.Response response = await http.post(routeUrl(createChatRoute),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode(jsonBody));
    final jsonData = json.decode(response.body);
    if (response.statusCode == 200) {
      _userIdChatListMap[receiverId]!
          .removeWhere((element) => element.id == randomNumber);
      MyChat respChat = MyChat.fromMap(jsonData['chat']);
      _userIdChatListMap[receiverId]!.insert(0, respChat);
      // print('chat details : ${jsonData['chat_details']}');
      Map<String, dynamic> detailsData = jsonData['chat_details'];
      int index =
          _chatDetailsList.indexWhere((element) => element.id == chatDetailsId);
      //
      final existing = _chatDetailsList[index];
      _chatDetailsList[index] = MyChatDetails(
        id: existing.id,
        userId1: existing.userId1,
        userId2: existing.userId2,
        unread1: detailsData['unread_1'] ?? existing.unread1,
        unread2: detailsData['unread_2'] ?? existing.unread2,
        mute1: detailsData['mute_1'] ?? existing.mute1,
        mute2: detailsData['mute_2'] ?? existing.mute2,
        lastMessage: detailsData['last_message'] ?? existing.lastMessage,
        lastMessageTime: detailsData['last_message_time'] != null
            ? DateTime.parse(detailsData['last_message_time'])
            : existing.lastMessageTime,
        firstName: existing.firstName,
        photosName: existing.photosName,
        updatedAt: detailsData['updated_at'] != null
            ? DateTime.parse(detailsData['updated_at'])
            : existing.updatedAt,
        createdAt: existing.createdAt,
      );
      // print(_chatDetailsList[index].lastMessage);
      // update offset //
      _userIdChatOffsetMap[receiverId] = _userIdChatOffsetMap[receiverId]! + 1;
      notifyListeners();
    }
    // print('chat created');
  }

  ////////////
  Future<void> updateChatDetailsRead(
      String chatDetailsId, int whichUser) async {
    // print('Update Chat Details Read...');
    Map jsonBody = {
      "user_id": _userId,
      "chat_details_id": chatDetailsId,
      "which_user": whichUser,
    };
    //
    http.Response response =
        await http.post(routeUrl(updateUnreadChatDetailsRoute),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $_token',
            },
            body: jsonEncode(jsonBody));
    final jsonData = json.decode(response.body);
    if (response.statusCode == 200) {
      int index =
          _chatDetailsList.indexWhere((element) => element.id == chatDetailsId);
      MyChatDetails previousChatDetails = _chatDetailsList[index];

      _chatDetailsList[index] = MyChatDetails(
        id: previousChatDetails.id,
        userId1: previousChatDetails.userId1,
        userId2: previousChatDetails.userId2,
        unread1: jsonData['unread_1'],
        unread2: jsonData['unread_2'],
        mute1: previousChatDetails.mute1,
        mute2: previousChatDetails.mute2,
        lastMessage: previousChatDetails.lastMessage,
        lastMessageTime: previousChatDetails.lastMessageTime,
        firstName: previousChatDetails.firstName,
        photosName: previousChatDetails.photosName,
        updatedAt: previousChatDetails.updatedAt,
        createdAt: previousChatDetails.createdAt,
      );
      notifyListeners();
    }
    // print('chat details read complete!');
  }

  Future<void> fetchChatsForUserId(String userId2, bool isFirst) async {
    // if (isFirst && _userIdChatListMap[userId2]!.isNotEmpty) {
    //   print('return empty chat');
    //   return;
    // }
    // print(
    //     'fetching chats for a varUserId : $userId2. Having offset: ${_userIdChatOffsetMap[userId2]}');
    Map jsonBody = {
      "user_id": _userId,
      "user_id_2": userId2,
      "offset": _userIdChatOffsetMap[userId2],
    };
    //
    http.Response response = await http.post(
      routeUrl(fetchChatRoute),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $_token',
      },
      body: jsonEncode(jsonBody),
    );
    final jsonData = json.decode(response.body);
    if (jsonData != null) {
      int total = jsonData.length;
      if (response.statusCode == 200) {
        if (jsonData.length > 0) {
          for (int i = 0; i < jsonData.length; i = i + 1) {
            MyChat _varChat = MyChat.fromMap(jsonData[i]);
            //
            _userIdChatListMap[userId2]!.add(_varChat);
            //
          }
          _userIdChatOffsetMap[userId2] =
              _userIdChatOffsetMap[userId2]! + total;
        }
        if (!isFirst) {
          notifyListeners();
        }
      }
    }
  }

  List<MyChat> getAllChatsForAUserId(String userId) {
    return _userIdChatListMap[userId]!;
  }

  //
  Future<void> fetchVarUserInfo(String varUserId) async {
    if (_userIdMatchedUserInfoMap[varUserId] != null) {
      return;
    }
    // Read value
    // print('fetching userinfo for varUserId : $varUserId');
    Map jsonBody = {"user_id": _userId, "var_user_id": varUserId};
    http.Response response = await http.post(routeUrl(getUserInfoRoute),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode(jsonBody));
    if (response.statusCode == 200) {
      final jsonData = json.decode(response.body);
      // print(jsonData);
      MyUserInfo _varMyUserInfo = MyUserInfo.fromMap(jsonData);
      _userIdMatchedUserInfoMap[varUserId] = _varMyUserInfo;
      //////
      fetchAllImages(_varMyUserInfo);
    }
  }

  ///
  Future<void> fetchMyUserInfo2() async {
    // Read value
    // print('fetching userinfo2...');
    Map jsonBody = {"user_id": _userId};
    http.Response response = await http.post(routeUrl(userInfo2FetchRoute),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode(jsonBody));
    //
    final jsonData = json.decode(response.body);
    if (response.statusCode == 200) {
      _myUserInfo2 = MyUserInfo2.fromMap(jsonData);
      updateUserInfo2Params(_myUserInfo2!);
      //////
      //
      _dataLoadedMap['myUserInfo2'] = true;
      //
    }
  }

  ///////
  // upload image to server
  Future<bool> verifyImage(File imageFile) async {
    print('Upload verify image....');
    // jsonBody['user_id'] = _userId;
    // print('1');
    //
    final request = http.MultipartRequest('POST', routeUrl(verifyImageRoute))
      ..headers.addAll({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $_token',
      })
      ..fields['user_id'] = _myUserInfo!.userId
      ..files.add(http.MultipartFile.fromBytes(
        'image',
        imageFile.readAsBytesSync(),
        filename: 'upload.webp',
      ));
    //
    final response = await request.send();
    final responseString = await response.stream.bytesToString();

    // Parse the response JSON and get the signed URL

    if (response.statusCode == 200) {
      // print('verify Image uploaded successfully');
      _verifyStatus = 0;
      notifyListeners();
      return true;
      // return signedUrl;
    } else {
      // print(response.request);
      // print('Error uploading verify image..!!');
      notifyListeners();
      return false;
    }
  }

  /////
  Future<void> fetchVerifyProfile() async {
    // print('Fetching Verify profile...');
    Map jsonBody = {};
    //
    jsonBody['user_id'] = _userId;
    http.Response response = await http.post(routeUrl(getVerifyImageRoute),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode(jsonBody));
    final jsonData = json.decode(response.body);
    // print(jsonData);
    if (response.statusCode == 200) {
      _verifyStatus = jsonData['status'];
      _dataLoadedMap['verifyProfile'] = true;
    }
  }

  ///////
  Future<Map<dynamic, dynamic>> fetchPayuHash(
    String userName,
    String orderId,
    double orderAmount,
    String hashName,
    String hashString,
    int plan_duration,
    int monthly_price,
  ) async {
    print('Fetch PayU Hash');
    //
    Map<dynamic, dynamic> result = {hashName: hashString};

    ///
    Map jsonBody = {
      "user_id": _userId,
      "user_name": userName,
      "order_id": orderId,
      "order_amount": orderAmount,
      "hash_name": hashName,
      "hash_string": hashString,
      "plan_duration": plan_duration,
      "monthly_price": monthly_price,
    };
    //
    // print('input:\n$jsonBody');
    if (hashName == "payment_related_details_for_mobile_sdk" ||
        hashName == "get_checkout_details" ||
        hashName == "payment_source") {
      http.Response response = await http.post(
        routeUrl(payURoute),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode(jsonBody),
      );
      // print(response.body);
      Map<dynamic, dynamic> jsonData = jsonDecode(response.body);
      // print(jsonData);
      if (response.statusCode == 200) {
        return jsonData;
      } else {
        return result;
      }
    } else {
      // print(result);
      return result;
    }
  }

  /////////
  Future<List<dynamic>> searchQuery(String userInput) async {
    print('Fetching sugegsted cities...');
    if (userInput.trim().length == 0 || userInput == null) {
      return [];
    }
    // print(_userId);
    // print(_token);
    Map jsonBody = {
      // "user_id": _userId,
      "user_input": userInput,
    };

    http.Response response = await http.post(routeUrl(citySuggestionRoute),
        headers: {
          'Content-Type': 'application/json',
          // 'Authorization': 'Bearer $_token',
        },
        body: jsonEncode(jsonBody));
    final jsonData = json.decode(response.body);
    // print(jsonData);
    // print('Total suggested citites: $total');
    if (response.statusCode == 200 && jsonData != null) {
      return jsonData;
    }
    return [];
  }

  //// change passport location ///
  Future<void> changePassportLocation(Map<String, dynamic> jsonBody) async {
    // print('Update Change passport location...');
    jsonBody["user_id"] = _userId;
    //
    http.Response response =
        await http.post(routeUrl(changePassportLocationRoute),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $_token',
            },
            body: jsonEncode(jsonBody));
    final jsonData = json.decode(response.body);
    //print(jsonData);
    if (response.statusCode == 200) {
      _myUserInfo = MyUserInfo.fromMap(jsonData['user_info']);
      _myUserInfo2 = MyUserInfo2.fromMap(jsonData['user_info_2']);
      notifyListeners();
    }
  }

  //////////////////////////////////////////
  Future<void> updateFCMDatabase() async {
    try {
      String? token = await FirebaseMessaging.instance.getToken();
      try {
        await FirebaseMessaging.instance.subscribeToTopic('all');
      } catch (error) {
        // print(error.toString());
      }
      if (token != null) {
        // print('Update FCM Token');
        Map<String, String> jsonBody = {
          "user_id": _myUserInfo!.userId,
          "fcm_token": token,
        };
        //
        http.Response response =
            await http.post(routeUrl(userInfo2FCMTokenRoute),
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': 'Bearer $_token',
                },
                body: jsonEncode(jsonBody));
        if (response.statusCode == 200) {
          // print('fcm token updated');
        }
      }
    } catch (error) {
      // print('Error-database-provider-updateFCMDatabase: $error');
    }
  }

  ////////////
  Future<void> unmatchChatDetails(String chatDetailsId, int whichUser) async {
    print('Unmatch user..');
    Map jsonBody = {
      "user_id": _userId,
      "chat_details_id": chatDetailsId,
      "which_user": whichUser,
    };
    //
    http.Response response = await http.post(routeUrl(unmatchChatDetailsRoute),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode(jsonBody));
    final jsonData = json.decode(response.body);
    if (response.statusCode == 200) {
      _chatDetailsList.removeWhere((element) => element.id == chatDetailsId);
      notifyListeners();
    }
  }

  //
  Future<bool> blockChatDetails(
      String chatDetailsId, int whichUser, String blockedReason) async {
    print('Block user..');
    Map jsonBody = {
      "user_id": _userId,
      "chat_details_id": chatDetailsId,
      "which_user": whichUser,
      "blocked_reason": blockedReason
    };
    //
    http.Response response = await http.post(routeUrl(blockChatDetailsRoute),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode(jsonBody));
    final jsonData = json.decode(response.body);
    if (response.statusCode == 200) {
      _chatDetailsList.removeWhere((element) => element.id == chatDetailsId);
      notifyListeners();
      return true;
    }
    return false;
  }

  Future<void> updateHideProfile() async {
    try {
      print('Update IsHide');
      Map<String, dynamic> jsonBody = {
        "user_id": _myUserInfo!.userId,
        "is_hide": !_myUserInfo!.isHide,
      };
      //
      http.Response response = await http.post(routeUrl(userInfoIsHideRoute),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $_token',
          },
          body: jsonEncode(jsonBody));
      if (response.statusCode == 200) {
        print('is hide updated');
        final jsonData = json.decode(response.body);
        _myUserInfo = MyUserInfo.fromMap(jsonData);
        notifyListeners();
      }
    } catch (error) {
      // print('Error-database-provider-IsHide: $error');
    }
  }

  Future<void> updateHideName() async {
    try {
      print('Update IsHide');
      Map<String, dynamic> jsonBody = {
        "user_id": _myUserInfo!.userId,
        "is_hide_name": !_myUserInfo!.isHideName,
      };
      //
      http.Response response =
          await http.post(routeUrl(userInfoIsHideNameRoute),
              headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer $_token',
              },
              body: jsonEncode(jsonBody));
      if (response.statusCode == 200) {
        print('is hide name updated');
        final jsonData = json.decode(response.body);
        _myUserInfo = MyUserInfo.fromMap(jsonData);
        notifyListeners();
      }
    } catch (error) {
      print('Error-database-provider-IsHide-Name: $error');
    }
  }

  // dio flutter image download
  Future<void> downloadTempImage(String url, String fileName) async {
    try {
      // Get the temporary directory
      final directory = await getTemporaryDirectory();
      _tempDirectoryForImage = '${directory.path}/images/';
      final filePath = '${directory.path}/images/$fileName';
      // Download the image
      await _dio.download(url, filePath);
      // print('filename:::: ${fileName}');
      // print(response);
      // print('done downlaod image: ${filePath}');
    } catch (e) {
      throw Exception('Failed to download image: $e');
    }
  }

  // referral //
  Future<bool> referral(String promoCode) async {
    try {
      print('Update Referral');
      Map<String, dynamic> jsonBody = {
        "user_id": _myUserInfo!.userId,
        "promo_code": promoCode,
      };
      //
      http.Response response = await http.post(routeUrl(referralRoute),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $_token',
          },
          body: jsonEncode(jsonBody));
      if (response.statusCode == 200) {
        print('is hide updated');
        final jsonData = json.decode(response.body);
        _myReferral = MyReferral.fromMap(jsonData);
        notifyListeners();
        return true;
      } else {
        return false;
      }
    } catch (error) {
      print('Error-database-provider-Referral: $error');
      return false;
    }
  }

  //
  // referral //
  Future<bool> referralBuyPremium() async {
    try {
      print('Update Referral');
      Map<String, dynamic> jsonBody = {
        "user_id": _myUserInfo!.userId,
      };
      //
      http.Response response =
          await http.post(routeUrl(referralBuyPremiumRoute),
              headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer $_token',
              },
              body: jsonEncode(jsonBody));
      if (response.statusCode == 200) {
        print('is hide updated');
        final jsonData = json.decode(response.body);
        _myReferral = MyReferral.fromMap(jsonData['referral']);
        _myUserInfo = MyUserInfo.fromMap(jsonData['user_info']);
        notifyListeners();
        return true;
      } else {
        return false;
      }
    } catch (error) {
      // print('Error-database-provider-Referral: $error');
      return false;
    }
  }

  // search movie
  Future<List> searchMovie(String query) async {
    try {
      print('Searching movie... [$query]');
      Map<String, dynamic> jsonBody = {
        "user_id": _myUserInfo!.userId,
        "query": query
      };
      //
      http.Response response = await http.post(routeUrl(searchMovieRoute),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $_token',
          },
          body: jsonEncode(jsonBody));
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        // print(data);
        // if (data['Response'] == 'True') {
        // setState(() {
        return data.where((movie) {
          return movie['Poster'] != 'N/A' && movie['Poster'] != null;
        }).toList();
        // });
        // } else {
        //   //  r setState(() {
        //   return [];
        //   // });
        // }
      } else {
        return [];
      }
    } catch (error) {
      print('Error-database-provider-Search-Movie: $error');
      return [];
    }
  }

  Future<bool> UserSelectMovie(String imdbId) async {
    try {
      print('Search movie...');
      Map<String, dynamic> jsonBody = {
        "user_id": _myUserInfo!.userId,
        "imdb_id": imdbId
      };
      //
      http.Response response = await http.post(routeUrl(selectMovieRoute),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $_token',
          },
          body: jsonEncode(jsonBody));
      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        _myUserInfo = MyUserInfo.fromMap(jsonData);
        notifyListeners();
        return true;
      } else {
        return false;
      }
    } catch (error) {
      print('Error-database-provider-Select-Movie: $error');
      return false;
    }
  }

  // delete a movie //
  Future<bool> UserDeleteAMovie(String imdbId) async {
    try {
      print('Search movie...');
      Map<String, dynamic> jsonBody = {
        "user_id": _myUserInfo!.userId,
        "imdb_id": imdbId
      };
      //
      http.Response response = await http.post(routeUrl(deleteMovieRoute),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $_token',
          },
          body: jsonEncode(jsonBody));
      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        _myUserInfo = MyUserInfo.fromMap(jsonData);
        notifyListeners();
        return true;
      } else {
        return false;
      }
    } catch (error) {
      print('Error-database-provider-Delete-Movie: $error');
      return false;
    }
  }

  // update personality traits value //
  Future<bool> UpdatePersonalityTraitValue(String _traitId, int value) async {
    print('inside update personality');
    try {
      // Update local state first for instant UI feedback
      final existingTraitIndex = _myUserInfo!.allInterests.personalityTraitsJson
          .indexWhere((trait) => trait.traitId == _traitId);

      if (existingTraitIndex != -1) {
        // If the trait exists, update its value
        final updatedTraits = _myUserInfo!.allInterests.personalityTraitsJson
            .map((trait) => trait.traitId == _traitId
                ? PersonalityTraitsJson(traitId: _traitId, value: value)
                : trait)
            .toList();

        _myUserInfo = _myUserInfo!.copyWith(
          allInterests: _myUserInfo!.allInterests.copyWith(
            personalityTraitsJson: updatedTraits,
          ),
        );
      } else {
        // If the trait doesn't exist, add it
        final newTrait = PersonalityTraitsJson(traitId: _traitId, value: value);
        final updatedTraits = [
          ..._myUserInfo!.allInterests.personalityTraitsJson,
          newTrait,
        ];

        _myUserInfo = _myUserInfo!.copyWith(
          allInterests: _myUserInfo!.allInterests.copyWith(
            personalityTraitsJson: updatedTraits,
          ),
        );
      }

      notifyListeners(); // Notify listeners to update the UI instantly
      print('Update Personality Trait Value..');
      Map<String, dynamic> jsonBody = {
        "user_id": _myUserInfo!.userId,
        "trait_id": _traitId,
        "value": value,
      };
      //

      http.Response response =
          await http.post(routeUrl(updatePersonalityTraitValueRoute),
              headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer $_token',
              },
              body: jsonEncode(jsonBody));
      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        // print(jsonData);
        _myUserInfo = MyUserInfo.fromMap(jsonData);
        return true;
      } else {
        return false;
      }
    } catch (error) {
      print('Error-database-provider-Update-Personality_Trait-Value: $error');
      return false;
    }
  }

  Future<bool> updatePersonalityTraitsBatch(Map<String, int> traits) async {
    print('inside update personality batch');
    try {
      // Update local state first for instant UI feedback
      final updatedTraits = _myUserInfo!.allInterests.personalityTraitsJson
          .map((trait) => traits.containsKey(trait.traitId)
              ? PersonalityTraitsJson(
                  traitId: trait.traitId, value: traits[trait.traitId]!)
              : trait)
          .toList();

      // Add new traits that didn't exist before
      final newTraits = traits.entries
          .where((entry) => !_myUserInfo!.allInterests.personalityTraitsJson
              .any((t) => t.traitId == entry.key))
          .map((entry) => PersonalityTraitsJson(
                traitId: entry.key,
                value: entry.value,
              ))
          .toList();

      _myUserInfo = _myUserInfo!.copyWith(
        allInterests: _myUserInfo!.allInterests.copyWith(
          personalityTraitsJson: [...updatedTraits, ...newTraits],
        ),
      );

      notifyListeners();

      // Prepare batch request
      final traitsList = traits.entries
          .map((e) => {'trait_id': e.key, 'value': e.value})
          .toList();

      final jsonBody = {
        "user_id": _myUserInfo!.userId,
        "traits": traitsList,
      };

      http.Response response = await http.post(
        routeUrl(
            updatePersonalityTraitsBatchRoute), // Make sure to define this route
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode(jsonBody),
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        _myUserInfo = MyUserInfo.fromMap(jsonData);
        return true;
      }
      return false;
    } catch (error) {
      print('Error updating personality traits batch: $error');
      return false;
    }
  }

  //
  Future<bool> UpdatePrompts(
      int promptNumber, String promptId, String promptValue) async {
    try {
      ////
      // ✅ Update local state first for instant UI feedback
      final existingPromptIndex = _myUserInfo!.allInterests.prompts
          .indexWhere((prompt) => prompt.id == promptId);

      if (existingPromptIndex != -1) {
        // If the prompt exists, update its value
        final updatedPrompts = _myUserInfo!.allInterests.prompts
            .map((prompt) => prompt.id == promptId
                ? InterestPrompt(id: promptId, value: promptValue)
                : prompt)
            .toList();

        _myUserInfo = _myUserInfo!.copyWith(
          allInterests:
              _myUserInfo!.allInterests.copyWith(prompts: updatedPrompts),
        );
      } else {
        // If the prompt doesn't exist, add it
        final newPrompt = InterestPrompt(
          id: promptId,
          value: promptValue,
        );
        final updatedPrompts = [
          ..._myUserInfo!.allInterests.prompts,
          newPrompt,
        ];

        _myUserInfo = _myUserInfo!.copyWith(
          allInterests: _myUserInfo!.allInterests.copyWith(
            prompts: updatedPrompts,
          ),
        );
      }

      notifyListeners(); // ✅ Notify listeners to update the UI instantly
      ////
      print('Update Personality Trait Value..');
      Map<String, dynamic> jsonBody = {
        "user_id": _myUserInfo!.userId,
        "prompt_id": promptId,
        "prompt_value": promptValue,
        "prompt_number": promptNumber,
      };
      //
      http.Response response = await http.post(routeUrl(userInfoPromptRoute),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $_token',
          },
          body: jsonEncode(jsonBody));
      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        _myUserInfo = MyUserInfo.fromMap(jsonData);
        notifyListeners();
        return true;
      } else {
        return false;
      }
    } catch (error) {
      print('Error-database-provider-Update-Prompts: $error');
      return false;
    }
  }

  // Add this new function
  Future<void> loadNextProfileImage() async {
    if (_nextProfileImageToLoad < _swipeScreenProfilesList.length) {
      MyUserInfo nextProfile =
          _swipeScreenProfilesList[_nextProfileImageToLoad];
      if (!_userSignedUrlMap.containsKey(nextProfile.userId)) {
        fetchAllImages(nextProfile);
      }
      _nextProfileImageToLoad++;
    }
  }

  // load data at once //
  ////////////////////////////
  Future<void> LoadAllRequest() async {
    print('START : Fetching all data at once');
    Map jsonBody = {
      "user_id": _userId,
    };
    //
    http.Response response = await http.post(routeUrl(loadAllRoute),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode(jsonBody));
    final jsonData = json.decode(response.body);
    //
    // int total = jsonData['total'] ?? 0;
    // print('Total Matched Profiles: $total');
    if (response.statusCode == 200) {
      // user_account //
      print('test-0');

      isBlocked = jsonData['user_account']['is_blocked'];
      _phoneNumber = jsonData['user_account']['phone_number'];
      // add all signed urls //
      // print(jsonData['signed_photos']);
      final signedPhotosMap = jsonData['signed_photos'] as Map<String, dynamic>;

      signedPhotosMap.forEach((userId, urls) {
        // print('updating signed url for $userId');
        _userPreSignedUrlMap[userId] = List<String>.from(urls as List<dynamic>);
      }); // all interest //
      // print('test-1');
      // print(jsonData['all_interest']);
      for (Map<String, dynamic> item in jsonData['all_interest']) {
        _allInterestList.add(MyInterest.fromMap(item));
      }
      // all personality traits
      // print('test-2');
      // print(jsonData['all_personality_traits']);
      for (Map<String, dynamic> item in jsonData['all_personality_traits']) {
        _allPersonalityTraitsList.add(MyPersonalityTraits.fromMap(item));
      }
      // print(jsonData['all_prompt_list']);
      // all prompt list //
      for (Map<String, dynamic> item in jsonData['all_prompt_list']) {
        _promptList.add(MyPrompt.fromMap(item));
      }
      print('test-3');
      // user info //
      _myUserInfo = MyUserInfo.fromMap(jsonData['user_info']);
      fetchAllImages(_myUserInfo!);
      // print('Helloooooo : ${_myUserInfo!.allInterests.interests}');
      // user info 2//
      _myUserInfo2 = MyUserInfo2.fromMap(jsonData['user_info_2']);
      updateUserInfo2Params(_myUserInfo2!);
      // user prefs //
      _myUserPrefs = MyUserPrefs.fromMap(jsonData['user_prefs']);
      // swipe profiles //
      int total = jsonData['total_swipe_profile'] ?? 0;
      // print('Total Profiles fetched: $total');
      if (total > 0) {
        for (int i = 0; i < total; i = i + 1) {
          MyUserInfo _userInfo =
              MyUserInfo.fromMap(jsonData['swipe_profiles'][i]);
          _swipeScreenProfilesList.add(_userInfo);
          // now call fetch images fucntion
          // Only load images for first 3 profiles
          if (i < 2) {
            fetchAllImages(_userInfo);
          }
          _nextProfileImageToLoad = 2; // Reset counter
          // fetchAllImages(_userInfo);
        }
      }
      _swipeScreenOffset += total;
      _dataLoadedMap['swipeScreen'] = true;
      if (total == 0) {
        _noSwipeProfilesLeft = true;
        // print('--- No Profiles Left ---');
        // notifyListeners();
      } else {}
      // who liked you //
      total = jsonData['total_who_liked_you'] ?? 0;
      print('Total Who Liked you Profiles: $total');
      if (total > 0) {
        for (int i = 0; i < total; i = i + 1) {
          MyUserInfo _userInfo =
              MyUserInfo.fromMap(jsonData['who_liked_you'][i]);
          _whoLikedYouProfilesList.add(_userInfo);
          // now add the message to map
          _whoLikedYouMessageMap[_userInfo.userId] =
              jsonData['who_liked_you'][i]['liked_message'];
          // now call fetch images fucntion
          fetchAllImages(_userInfo);
        }
      }
      print('Test1');

      if (_dataLoadedMap['whoLikedYouScreen'] == false) {
        _dataLoadedMap['whoLikedYouScreen'] = true;
      }
      _whoLikedYouScreenOffset += total;
      if (total == 0) {
        _noWhoLikedYouProfilesLeft = true;
        // print('--- No Who liked you Profiles Left ---');
        // notifyListeners();
      } else {}
      // similar profiles //
      if (jsonData['similar_profiles'] != null) {
        List<String> user_id_list = [];
        user_id_list =
            List<String>.from(jsonData["similar_profiles"]['profile_list']);
        for (String var_user_id in user_id_list) {
          // MyUserInfo? varUserInfo =
          fetchVarUserInfoForSimilarProfile(var_user_id).then((varUserInfo) {
            _similarProfilesList.add(varUserInfo);
            _similarProfilesUserIdList.add(varUserInfo.userId);
          });
        }
      }
      print('Test2');

      if (_dataLoadedMap['similarScreen'] == false) {
        _dataLoadedMap['similarScreen'] = true;
      }
      // all chated details (all matched profiles)//
      total = jsonData['total_chat_details'] ?? 0;
      if (total > 0) {
        for (int i = 0; i < total; i = i + 1) {
          MyChatDetails _matchedChatDetails =
              MyChatDetails.fromMap(jsonData['chat_details'][i]);
          _chatDetailsList.add(_matchedChatDetails);
          // now call fetch images fucntion
          // fetchAllImages(_userInfo);
          String userId = findVarUserIdFromChatDetails(
              _matchedChatDetails, _myUserInfo!.userId);
          fetchAImage(userId, _matchedChatDetails.photosName[0]);
          // now make map for this //
          _userIdChatListMap[userId] = [];
          _userIdChatOffsetMap[userId] = 0;
          fetchChatsForUserId(userId, true);
          //
        }
        if (_dataLoadedMap['matchedProfiles'] == false) {
          _dataLoadedMap['matchedProfiles'] = true;
        }
      }
      print('Test3');

      // verify profile //
      _verifyStatus = jsonData['verify_profile']['status'];
      _dataLoadedMap['verifyProfile'] = true;
      print('END: Fetching all data at once - SUCCESS');
      //
      _dataLoadedMap['loadall'] = true;
      //
      // referral //
      _myReferral = MyReferral.fromMap(jsonData['referral']);
      // _dataLoadedMap['verifyProfile'] = true;
      print('END: Fetching all data at once - SUCCESS');
      //
      _dataLoadedMap['loadall'] = true;
      //
    } else {
      print('END: Fetching all data at once - FAILED');
    }
  }

  /// logout
  void logout() async {
    resetAll();
  }

  // cashfree payment logic //
  // referral //
  Future<Map<String, dynamic>> cashfreeCreateOrder(
      double orderAmount, int planDuration, int monthlyPrice) async {
    try {
      print('Cashfree Create Order');
      Map<String, dynamic> jsonBody = {
        "user_id": _myUserInfo!.userId,
        "phone_number": _phoneNumber.toString(),
        "order_amount": orderAmount,
        "plan_duration": planDuration,
        "monthly_price": monthlyPrice,
      };
      //
      http.Response response = await http.post(routeUrl(cashfreeOrderRoute),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $_token',
          },
          body: jsonEncode(jsonBody));
      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        // print(jsonData);
        // notifyListeners();
        return jsonData;
      } else {
        return {};
      }
    } catch (error) {
      print('Error-database-provider-Cashfree: $error');
      return {};
    }
  }

  /// delete account ii.e. udpate userinfo to isdeleted to true///
  Future<void> updateUserInfoIsDeleted(bool value) async {
    print('updating user info is deleted:');
    Map<String, dynamic> jsonBody = {
      'user_id': _myUserInfo!.userId,
      'is_deleted': value
    };
    http.post(routeUrl(userInfoDeleteRoute),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode(jsonBody));
    resetAll();
    // final jsonData = json.decode(response.body);
    // now reset all to logout //
    //
  }

  Future<void> updateHideProfileToTrue() async {
    try {
      print('Update IsHide');
      Map<String, dynamic> jsonBody = {
        "user_id": _myUserInfo!.userId,
        "is_hide": true,
      };
      //
      http.Response response = await http.post(routeUrl(userInfoIsHideRoute),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $_token',
          },
          body: jsonEncode(jsonBody));
      if (response.statusCode == 200) {
        print('is hide updated');
        final jsonData = json.decode(response.body);
        _myUserInfo = MyUserInfo.fromMap(jsonData);
        notifyListeners();
      }
    } catch (error) {
      print('Error-database-provider-IsHide: $error');
    }
  }

  //////////

  /////// REALTIME /////////
  //////////////////////////////////////////////////////////////////////
  ///////////////////////////////////////////////////////////////////////
  /// realtime ///
  // In DatabaseProvider
  DateTime? _lastOnlineTime;

  void _handleNewMessage(MyChat msg) {
    _userIdChatListMap[msg.senderId] ??= [];
    _userIdChatListMap[msg.senderId]!.insert(0, msg);
    notifyListeners();
  }

  void _updateLastOnlineTime() {
    _lastOnlineTime = DateTime.now().toUtc();
    print("Went offline at: $_lastOnlineTime");
  }

  Future<void> _fetchMissedMessages() async {
    if (_lastOnlineTime == null) return;
    print('Syncing messages...');

    final response = await http.post(
      routeUrl(missedMessagesRoute),
      body: jsonEncode({
        'user_id': _userId,
        'last_msg_time': _lastOnlineTime!.toIso8601String(),
      }),
      headers: {'Authorization': 'Bearer $_token'},
    );

    if (response.statusCode == 200) {
      final messages = json.decode(response.body) as List;
      if (messages.isNotEmpty) {
        for (var msg in messages) {
          _handleNewMessage(MyChat.fromMap(msg));
        }
      }
    }
    print('Syncing messaged done!');
  }

  // Add these class variables
  StreamSubscription? _sseSubscription;
  bool _isSSEConnected = false;

// Modified listenToSSE function
  Future<void> listenToSSE() async {
    if (_isSSEConnected) return;

    print('Connecting to SSE...');
    final url = appUrl + '/' + realtimeRoute;

    try {
      _sseSubscription?.cancel();
      await _fetchMissedMessages(); // Fetch missed messages first
      _sseSubscription = SSEClient.subscribeToSSE(
        url: url,
        header: {
          'Authorization': 'Bearer $_token',
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache',
        },
      ).listen(
        (event) {
          _isSSEConnected = true;
          _handleSSEEvent(event);
        },
        onError: (error) {
          _isSSEConnected = false;
          _updateLastOnlineTime();
          print('SSE Error: $error');
          _reconnectSSE();
        },
        onDone: () {
          _isSSEConnected = false;
          _updateLastOnlineTime();
          print('SSE Connection closed');
          _reconnectSSE();
        },
      );
    } catch (e) {
      print('SSE Connection failed: $e');
      _reconnectSSE();
    }
  }

  void _reconnectSSE() {
    Future.delayed(const Duration(seconds: 5), () {
      print('Attempting SSE reconnection...');
      listenToSSE();
    });
  }

  void _handleSSEEvent(SSEModel event) {
    try {
      final jsonData = json.decode(event.data!);
      // print('SSE Event: ${event.event}');

      // if (event.event == 'chat') {
      if (event.event == 'new_message') {
        _handleCombinedChatEvent(jsonData);
        // _handleIncomingChat(jsonData);
      } else if (event.event == 'chat_details') {
        _handleChatDetailsUpdate(jsonData);
      } else if (event.event == 'user_info') {
        _handleUserInfoUpdate(jsonData);
      } else if (event.event == 'connected') {
        print('SSE Connection established');
      } else if (event.event == 'heartbeat') {
        print("heartbeat..");
      }
    } catch (e) {
      print('Error processing SSE event: $e');
    }
  }

  // / NEW: Combined handler for chat + details
  void _handleCombinedChatEvent(Map<String, dynamic> jsonData) {
    // print('RAW SSE COMBINED DATA: $jsonData');

    try {
      // final data = jsonData as Map<String, dynamic>;
      final chatData = jsonData['chat'] as Map<String, dynamic>;
      final detailsData = jsonData['chat_details'] as Map<String, dynamic>;

      // 1. Process the chat message
      if (chatData['reciever_id'] == _userId) {
        final chat = MyChat.fromMap(chatData);

        _userIdChatListMap[chat.senderId] ??= [];
        final existingIndex = _userIdChatListMap[chat.senderId]!
            .indexWhere((m) => m.id == chat.id);

        if (existingIndex == -1 ||
            _userIdChatListMap[chat.senderId]![existingIndex].message !=
                chat.message) {
          if (existingIndex != -1) {
            _userIdChatListMap[chat.senderId]!.removeAt(existingIndex);
          }
          _userIdChatListMap[chat.senderId]!.insert(0, chat);
        }
      }

      // 2. Process chat details
      final chatDetailsId = detailsData['id'];
      final existingIndex =
          _chatDetailsList.indexWhere((cd) => cd.id == chatDetailsId);

      if (existingIndex != -1) {
        final existing = _chatDetailsList[existingIndex];
        _chatDetailsList[existingIndex] = MyChatDetails(
          id: existing.id,
          userId1: existing.userId1,
          userId2: existing.userId2,
          unread1: detailsData['unread_1'] ?? existing.unread1,
          unread2: detailsData['unread_2'] ?? existing.unread2,
          mute1: detailsData['mute_1'] ?? existing.mute1,
          mute2: detailsData['mute_2'] ?? existing.mute2,
          lastMessage: detailsData['last_message'] ?? existing.lastMessage,
          lastMessageTime: detailsData['last_message_time'] != null
              ? DateTime.parse(detailsData['last_message_time'])
              : existing.lastMessageTime,
          firstName: existing.firstName,
          photosName: existing.photosName,
          updatedAt: detailsData['updated_at'] != null
              ? DateTime.parse(detailsData['updated_at'])
              : existing.updatedAt,
          createdAt: existing.createdAt,
        );
      }

      notifyListeners();
    } catch (e) {
      print('Error processing combined chat event: $e');
    }
  }

  void _handleIncomingChat(Map<String, dynamic> jsonData) {
    // print('RAW SSE CHAT DATA: $jsonData'); // Add this line
    try {
      final chat = MyChat.fromMap(jsonData);
      if (chat.recieverId != _userId) return;

      _userIdChatListMap[chat.senderId] ??= [];
      final existingIndex =
          _userIdChatListMap[chat.senderId]!.indexWhere((m) => m.id == chat.id);

      // Only add if not found OR if found but with different content
      if (existingIndex == -1 ||
          _userIdChatListMap[chat.senderId]![existingIndex].message !=
              chat.message) {
        if (existingIndex != -1) {
          _userIdChatListMap[chat.senderId]!.removeAt(existingIndex);
        }
        _userIdChatListMap[chat.senderId]!.insert(0, chat);
        notifyListeners();
      }
    } catch (e) {
      print('Error processing chat: $e');
    }
  }

  void _handleChatDetailsUpdate(Map<String, dynamic> jsonData) {
    // print('RAW SSE CHAT DETAILS: $jsonData'); // Add this line

    try {
      final chatDetailsId = jsonData['id'];
      final existingIndex =
          _chatDetailsList.indexWhere((cd) => cd.id == chatDetailsId);

      if (existingIndex != -1) {
        final existing = _chatDetailsList[existingIndex];
        _chatDetailsList[existingIndex] = MyChatDetails(
          id: existing.id,
          userId1: existing.userId1,
          userId2: existing.userId2,
          unread1: jsonData['unread_1'] ?? existing.unread1,
          unread2: jsonData['unread_2'] ?? existing.unread2,
          mute1: jsonData['mute_1'] ?? existing.mute1,
          mute2: jsonData['mute_2'] ?? existing.mute2,
          lastMessage: jsonData['last_message'] ?? existing.lastMessage,
          lastMessageTime: jsonData['last_message_time'] != null
              ? DateTime.parse(jsonData['last_message_time'])
              : existing.lastMessageTime,
          firstName: existing.firstName,
          photosName: existing.photosName,
          updatedAt: jsonData['updated_at'] != null
              ? DateTime.parse(jsonData['updated_at'])
              : existing.updatedAt,
          createdAt: existing.createdAt,
        );
        notifyListeners();
      }
    } catch (e) {
      // print('Error handling chat details update: $e');
    }
  }

  void _handleUserInfoUpdate(Map<String, dynamic> jsonData) {
    // print('RAW USERINFO UPDATE: $jsonData'); // Add this line

    try {
      final updatedUser = MyUserInfo.fromMap(jsonData);

      if (updatedUser.userId == _userId) {
        _myUserInfo = updatedUser;
        notifyListeners();
      } else if (_userIdMatchedUserInfoMap.containsKey(updatedUser.userId)) {
        _userIdMatchedUserInfoMap[updatedUser.userId] = updatedUser;
        notifyListeners();
      }
    } catch (e) {
      // print('Error handling user info update: $e');
    }
  }

  @override
  void dispose() {
    _sseSubscription?.cancel();
    super.dispose();
  }
//   Future<void> listenToSSE() async {
//     String url = appUrl + '/' + realtimeRoute;
//     SSEClient.subscribeToSSE(url: url, header: {
//       "Accept": "text/event-stream",
//       "Cache-Control": "no-cache",
//     }).listen((event) {
//       print('data receievd');
//       // print('Id: ' + event.id!);
//       print('Event: ' + event.event!);
//       print('Data: ' + event.data!);
//       print(event);
//       final jsonData = json.decode(event.data!);
//       print(jsonData);
//       //
//       if (event.event == 'chat') {
//         print('inside chat function');
//         MyChat _varChat = MyChat.fromMap(jsonData);
//         if (_varChat.recieverId == _myUserInfo!.userId) {
//           print('For me');
//           _userIdChatListMap[_varChat.senderId]!.insert(0, _varChat);
//           _userIdChatOffsetMap[_varChat.senderId] =
//               _userIdChatOffsetMap[_varChat.senderId]! + 1;
//           notifyListeners();
//         }
//       } else if (event.event == 'chat_details') {
//         //
//         Map<String, dynamic> _varChatDetailsMap = jsonData;
//         //first cehck if it is for me
//         if (_varChatDetailsMap['user_id_1'] == _myUserInfo!.userId ||
//             _varChatDetailsMap['user_id_2'] == _myUserInfo!.userId) {
//           //
//           if (_varChatDetailsMap['blocked_1'] == true ||
//               _varChatDetailsMap['blocked_2'] == true ||
//               _varChatDetailsMap['unmatched_1'] == true ||
//               _varChatDetailsMap['unmatched_2'] == true) {
//             // Case1: if chatdetails blocked_1/2 or unmatched1/2 is true then delete it if it is their
//             _chatDetailsList.removeWhere(
//                 (element) => element.id == _varChatDetailsMap['id']);
//             notifyListeners();
//           } else {
//             ////
//             // Case2: normal updates-> now update chat details if it is for me //

//             MyChatDetails varChatDetails = _chatDetailsList.firstWhere(
//               (element) => element.id == _varChatDetailsMap['id'],
//             );
//             MyChatDetails udpatedChatDetails = MyChatDetails(
//               id: varChatDetails.id,
//               userId1: varChatDetails.userId1,
//               userId2: varChatDetails.userId2,
//               unread1: _varChatDetailsMap['unread_1'],
//               unread2: _varChatDetailsMap['unread_2'],
//               mute1: varChatDetails.mute1,
//               mute2: varChatDetails.mute2,
//               lastMessage: _varChatDetailsMap['last_message'],
//               lastMessageTime:
//                   DateTime.parse(_varChatDetailsMap['last_message_time']),
//               firstName: varChatDetails.firstName,
//               photosName: varChatDetails.photosName,
//               updatedAt: DateTime.parse(_varChatDetailsMap['updated_at']),
//               createdAt: DateTime.parse(_varChatDetailsMap['created_at']),
//             );
//             // now add
//             _chatDetailsList[_chatDetailsList
//                     .indexWhere((element) => element.id == varChatDetails.id)] =
//                 udpatedChatDetails;
//             notifyListeners();
//           }
//         }
//       } else if (event.event.toString() == 'user_info') {
//         print('1');
//         MyUserInfo varUserInfo = MyUserInfo.fromMap(jsonData);
//         print('2');
//         if (varUserInfo.userId == _myUserInfo!.userId) {
//           _myUserInfo = varUserInfo;
//           notifyListeners();
//         }
//       }
//       // now add data to chat if id is mine
//     });
//   }
// }
}
