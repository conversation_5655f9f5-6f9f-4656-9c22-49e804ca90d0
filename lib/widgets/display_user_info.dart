import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/widgets.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:zupid/common_things/images.dart';
import 'package:zupid/common_things/my_colors.dart';
import 'package:zupid/providers/auth_provider.dart';
import 'package:zupid/providers/database_provider.dart';
import 'package:zupid/translations/locale_keys.g.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';

import 'package:zupid/common_things/my_fucntions.dart';
import 'package:zupid/models/myPromptList.dart';
import 'package:zupid/models/myUserInfo.dart';
import 'package:zupid/widgets/each_container_item.dart';
import 'package:zupid/widgets/interest_container.dart';
import 'package:zupid/widgets/loading_widget.dart';

class DisplayPicEachItem extends StatelessWidget {
  final String photoLink;
  final bool maintainBox;
  const DisplayPicEachItem({
    required this.photoLink,
    required this.maintainBox,
  });
  @override
  Widget build(BuildContext context) {
    final tempImageDirectory =
        Provider.of<DatabaseProvider>(context, listen: true)
            .tempDirectoryForImage;

    return photoLink == "-1" && maintainBox == false
        ? SizedBox()
        : Column(
            children: [
              Padding(
                padding: EdgeInsets.all(.5.w),
                child: Container(
                  // height: eachPhotoHeight,
                  // width: 100.w,
                  // height: 60.h,
                  decoration: BoxDecoration(
                    border: Border.all(
                        color: mainContrastColor.withOpacity(0.5),
                        width: .5.sp),
                    borderRadius: BorderRadius.circular(1.h),
                  ),
                  // margin: EdgeInsets.all(1.h),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(.9.h),
                    child: SizedBox(
                      height: 100.w,
                      width: 100.w,
                      child: photoLink == "isLoading"
                          ? LoadingWidget()
                          : FittedBox(
                              fit: BoxFit.cover,
                              // child: Image.asset(
                              //   'assets/images/swipe-images/${index}.jpeg',
                              // ),
                              child: photoLink == "-1"
                                  ? Image.asset(
                                      // height: eachPhotoHeight / 4,
                                      MyImages.kNoPhoto)
                                  : Image.file(
                                      File(tempImageDirectory + photoLink))

                              // CachedNetworkImage(
                              //     imageUrl: photoLink,
                              //   ),
                              ),
                    ),
                  ),
                ),
              ),
              SizedBox(height: .5.h),
            ],
          );
  }
}

class UserDetailsItem extends StatelessWidget {
  final MyUserInfo varUserInfo;
  final int age;
  final int distance;
  final bool column2Show;
  final double resizeFactor;

  const UserDetailsItem(this.varUserInfo, this.age, this.distance,
      this.column2Show, this.resizeFactor);

  @override
  Widget build(BuildContext context) {
    final authProv = Provider.of<AuthProvider>(context, listen: false);
    return Padding(
      padding: EdgeInsets.fromLTRB(2.w, 1.h, 2.w, 1.h),
      child: Column(
        // mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        overflow: TextOverflow.ellipsis,
                        varUserInfo.isHideName
                            ? '${showOnlyFirstLetter(varUserInfo.firstName)}, $age'
                            : '${capitalizeFirstLetter(varUserInfo.firstName)}, $age',
                        style: TextStyle(
                          fontSize: 16.sp * resizeFactor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(width: 2.w * resizeFactor),
                      if (varUserInfo.isVerified)
                        Icon(
                          Icons.verified,
                          color: Colors.blue,
                          size: 14.sp * resizeFactor,
                        ),
                    ],
                  ),
                  SizedBox(height: .5.h * resizeFactor),
                  // Text(
                  //   varUserInfo.cityName,
                  //   style: TextStyle(
                  //     fontSize: 9.sp * resizeFactor,
                  //     fontWeight: FontWeight.w700,
                  //   ),
                  // ),
                  // SizedBox(height: .5.h * resizeFactor),
                  // Text(
                  //   distance != -1
                  //       ? ""
                  //       : '$distance ${LocaleKeys.Common_Km_Away.tr()}',
                  //   style: TextStyle(
                  //     fontSize: 9.sp * resizeFactor,
                  //     fontWeight: FontWeight.w700,
                  //   ),
                  // ),
                ],
              ),
              if (column2Show)
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    // if (varUserInfo.work != '')
                    //   Row(
                    //     children: [
                    //       Text(
                    //         varUserInfo.work,
                    //         style: TextStyle(
                    //             fontSize: 9.sp * resizeFactor,
                    //             fontWeight: FontWeight.w700),
                    //       ),
                    //       SizedBox(
                    //         width: 2.w * resizeFactor,
                    //       ),
                    //       Icon(
                    //         Icons.work,
                    //         size: 9.sp * resizeFactor,
                    //       ),
                    //     ],
                    //   ),
                    // SizedBox(height: .2.h * resizeFactor),
                    // if (varUserInfo.school != '')
                    //   Row(
                    //     children: [
                    //       Text(
                    //         varUserInfo.school,
                    //         style: TextStyle(
                    //             fontSize: 9.sp * resizeFactor,
                    //             fontWeight: FontWeight.w700),
                    //       ),
                    //       SizedBox(width: 2.w * resizeFactor),
                    //       Icon(
                    //         Icons.school_rounded,
                    //         size: 9.sp * resizeFactor,
                    //       ),
                    //     ],
                    //   ),
                    // SizedBox(height: .2.h * resizeFactor),
                    // Text(
                    //   varUserInfo.cityName,
                    //   style: TextStyle(
                    //     fontSize: 9.sp * resizeFactor,
                    //     fontWeight: FontWeight.w700,
                    //   ),
                    // ),
                    // SizedBox(height: .5.h * resizeFactor),
                    // Text(
                    //   distance != -1
                    //       ? ""
                    //       : '$distance ${LocaleKeys.Common_Km_Away.tr()}',
                    //   style: TextStyle(
                    //     fontSize: 9.sp * resizeFactor,
                    //     fontWeight: FontWeight.w700,
                    //   ),
                    // ),
                    if (distance != -1)
                      Row(
                        children: [
                          Text(
                            '$distance ${LocaleKeys.Common_Km_Away.tr()}',
                            // findLookingFor(varUserInfo.lookingFor),
                            style: TextStyle(
                                fontSize: 9.sp * resizeFactor,
                                fontWeight: FontWeight.w700),
                          ),
                          SizedBox(
                            width: 1.w * resizeFactor,
                          ),
                          Icon(
                            Icons.directions_walk,
                            size: 11.sp * resizeFactor,
                          ),
                        ],
                      ),
                  ],
                ),
            ],
          ),
          SizedBox(
            height: 1.h,
          ),
          // new additon for zupid
          Wrap(
            crossAxisAlignment: WrapCrossAlignment.start,
            spacing: 1.w,
            runSpacing: 1.w,
            alignment: WrapAlignment.start,
            children: [
              LifestyleWidgetDisplay(
                iconData: Icons.person_rounded,
                myText: varUserInfo.sex == 0
                    ? "Women"
                    : varUserInfo.sex == 1
                        ? "Man"
                        : "Non-binary",
                resizeFactor: resizeFactor,
              ),
              LifestyleWidgetDisplay(
                iconData: Icons.height_rounded,
                myText: '${varUserInfo.height.toString()} cm',
                resizeFactor: resizeFactor,
              ),
              if (varUserInfo.cityName.trim() != "")
                LifestyleWidgetDisplay(
                  iconData: Icons.location_city_rounded,
                  myText: varUserInfo.cityName,
                  resizeFactor: resizeFactor,
                ),
              LifestyleWidgetDisplay(
                iconData: Icons.search_rounded,
                myText: findLookingFor(varUserInfo.lookingFor),
                resizeFactor: resizeFactor,
              ),
              if (varUserInfo.sexuality != 0)
                LifestyleWidgetDisplay(
                  iconData: Icons.male_rounded,
                  myText: getKeyFromValue(
                      authProv.getSexuality, varUserInfo.sexuality)!,
                  resizeFactor: resizeFactor,
                ),
              if (varUserInfo.work != "")
                LifestyleWidgetDisplay(
                  iconData: Icons.work_rounded,
                  myText: varUserInfo.work,
                  resizeFactor: resizeFactor,
                ),
              if (varUserInfo.school != "")
                LifestyleWidgetDisplay(
                  iconData: Icons.account_balance_rounded,
                  myText: varUserInfo.school,
                  resizeFactor: resizeFactor,
                ),
              if (varUserInfo.educationLevel != 0)
                LifestyleWidgetDisplay(
                  iconData: Icons.school_rounded,
                  myText: getKeyFromValue(
                      authProv.getEducation, varUserInfo.educationLevel)!,
                  resizeFactor: resizeFactor,
                ),
              if (varUserInfo.religion != 0)
                LifestyleWidgetDisplay(
                  iconData: Icons.temple_hindu_rounded,
                  myText: getKeyFromValue(
                      authProv.getReligion, varUserInfo.religion)!,
                  resizeFactor: resizeFactor,
                ),
              if (varUserInfo.zodiac != 0)
                LifestyleWidgetDisplay(
                  iconData: Icons.star_rounded,
                  myText:
                      getKeyFromValue(authProv.getZodiac, varUserInfo.zodiac)!,
                  resizeFactor: resizeFactor,
                ),
              if (varUserInfo.drinking != 0)
                LifestyleWidgetDisplay(
                  iconData: Icons.local_bar,
                  myText: getKeyFromValue(
                      authProv.getDrinking, varUserInfo.drinking)!,
                  resizeFactor: resizeFactor,
                ),
              if (varUserInfo.smoking != 0)
                LifestyleWidgetDisplay(
                  iconData: Icons.smoking_rooms_rounded,
                  myText: getKeyFromValue(
                      authProv.getSmoking, varUserInfo.smoking)!,
                  resizeFactor: resizeFactor,
                ),
              if (varUserInfo.drugs != 0)
                LifestyleWidgetDisplay(
                  iconData: FontAwesomeIcons.capsules,
                  myText:
                      getKeyFromValue(authProv.getDrugs, varUserInfo.drugs)!,
                  resizeFactor: resizeFactor,
                ),
              if (varUserInfo.marijuana != 0)
                LifestyleWidgetDisplay(
                  iconData: FontAwesomeIcons.cannabis,
                  myText: getKeyFromValue(
                      authProv.getMarijuana, varUserInfo.marijuana)!,
                  resizeFactor: resizeFactor,
                ),
              if (varUserInfo.children != 0)
                LifestyleWidgetDisplay(
                  iconData: Icons.child_friendly_rounded,
                  myText: getKeyFromValue(
                      authProv.getChildren, varUserInfo.children)!,
                  resizeFactor: resizeFactor,
                ),
            ],
          )
        ],
      ),
    );
  }
}

class LifestyleWidgetDisplay extends StatelessWidget {
  const LifestyleWidgetDisplay(
      {super.key,
      required this.resizeFactor,
      required this.iconData,
      required this.myText});

  final double resizeFactor;
  final IconData iconData;
  final String myText;

  @override
  Widget build(BuildContext context) {
    return IntrinsicWidth(
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 1.w),
        decoration: BoxDecoration(
          color: mainContrastColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          // mainAxisSize: MainAxisSize.m,
          children: [
            Icon(iconData, size: 9.sp * resizeFactor),
            SizedBox(width: 1.w),
            Text(
              myText,
              style: TextStyle(
                  fontSize: 9.sp * resizeFactor, fontWeight: FontWeight.w700),
            ),
          ],
        ),
      ),
    );
  }
}

class CompleteUserInfoDisplay extends StatelessWidget {
  // top details
  final List<String> photoLink;
  // middle
  final MyUserInfo varUserInfo;
  final int age;
  final int distance;
  final bool column2Show;
  final double resizeFactor;
  // bottom
  final List<MyPrompt> promptList;

  const CompleteUserInfoDisplay({
    required this.photoLink,
    required this.varUserInfo,
    required this.age,
    required this.distance,
    required this.column2Show,
    required this.resizeFactor,
    required this.promptList,
  });
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        UserDetailsItem(varUserInfo, age, distance, column2Show, 1),
        SizedBox(height: 1.h),
        DisplayPicEachItem(photoLink: photoLink[0], maintainBox: true),
        DisplayPicEachItem(photoLink: photoLink[1], maintainBox: true),
        DisplayPicEachItem(photoLink: photoLink[2], maintainBox: false),
        DisplayPicEachItem(photoLink: photoLink[3], maintainBox: false),
        DisplayPicEachItem(photoLink: photoLink[4], maintainBox: false),
        DisplayPicEachItem(photoLink: photoLink[5], maintainBox: false),
        SizedBox(height: 10.h),
      ],
    );
  }
}
